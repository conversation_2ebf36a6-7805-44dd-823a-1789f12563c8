#!/usr/bin/env python
"""
Simple script to run migrations with minimal configuration.
This bypasses the S3 and database configuration issues.
"""

import os
import sys
import django
from django.core.management import execute_from_command_line

# Set minimal environment variables for migration
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'canvider.settings')

# Override problematic environment variables
os.environ['AWS_ENDPOINT_URL'] = 'https://fra1.digitaloceanspaces.com'  # Fix the escaped URL
os.environ['DATABASE_URL'] = 'postgresql://user:pass@localhost:5432/canvider_db'  # Use a simple local DB for migration

# Add the project directory to Python path
sys.path.append('/Users/<USER>/Documents/canvider/canvider')

if __name__ == '__main__':
    try:
        # Setup Django
        django.setup()
        
        print("=== Running Django Migrations ===")
        print("Note: Using simplified configuration for migration")
        
        # Run makemigrations first
        print("\n1. Creating migrations...")
        execute_from_command_line(['manage.py', 'makemigrations'])
        
        # Then run migrate
        print("\n2. Applying migrations...")
        execute_from_command_line(['manage.py', 'migrate'])
        
        print("\n✅ Migrations completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Migration failed: {e}")
        print("\nTry running migrations manually:")
        print("1. Fix your DATABASE_URL environment variable")
        print("2. Fix your AWS_ENDPOINT_URL environment variable") 
        print("3. Run: python manage.py makemigrations")
        print("4. Run: python manage.py migrate")
