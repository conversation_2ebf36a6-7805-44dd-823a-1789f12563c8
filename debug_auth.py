#!/usr/bin/env python
"""
Debug script for authentication system.
Run this to check if your authentication setup is correct.
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SECRET_KEY', 'debug-key-only')
os.environ.setdefault('DEBUG', '1')

# Add the project directory to Python path
sys.path.append('/Users/<USER>/Documents/canvider/canvider')

# Setup Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'canvider.settings')
django.setup()

from django.contrib.auth.models import User
from feed.models import Employee, Employer, JobTemplate, TalentPool, Invitation

def main():
    print("=== Authentication System Debug ===\n")
    
    # Check Users
    users = User.objects.all()
    print(f"Total Users: {users.count()}")
    
    if users.count() == 0:
        print("❌ No users found! You need to create users first.")
        print("   Run: python manage.py createsuperuser")
        return
    
    for user in users:
        print(f"  👤 User: {user.username}")
        print(f"     Email: {user.email}")
        print(f"     Active: {user.is_active}, Staff: {user.is_staff}, Superuser: {user.is_superuser}")
        print(f"     🔑 Login with: '{user.email}' (email) or '{user.username}' (username)")

        # Check if user has employee record
        try:
            employee = user.employee
            print(f"     ✅ Employee: {employee.role} - Status: {employee.status}")
            print(f"     🏢 Employer: {employee.employer_id.employer_name} (ID: {employee.employer_id.employer_id})")
        except AttributeError:
            print(f"     ❌ ERROR: No Employee record for user {user.username}")
            print(f"        You need to create an Employee record for this user!")
    
    print()
    
    # Check Employers
    employers = Employer.objects.all()
    print(f"Total Employers: {employers.count()}")
    
    if employers.count() == 0:
        print("❌ No employers found! You need to create employers first.")
        return
    
    for employer in employers:
        print(f"  🏢 Employer: {employer.employer_name} (ID: {employer.employer_id})")
        employee_count = Employee.objects.filter(employer_id=employer).count()
        print(f"     Employees: {employee_count}")
    
    print()
    
    # Check Employees
    employees = Employee.objects.all()
    print(f"Total Employees: {employees.count()}")
    
    if employees.count() == 0:
        print("❌ No employees found! You need to create employee records.")
        print("   Each User must have a corresponding Employee record.")
        return
    
    for employee in employees:
        print(f"  👨‍💼 Employee: {employee.user.username}")
        print(f"      Role: {employee.role}")
        print(f"      Status: {employee.status}")
        print(f"      Employer: {employee.employer_id.employer_name}")
    
    print("\n=== Recommendations ===")
    
    # Check for users without employee records
    users_without_employees = []
    for user in users:
        try:
            user.employee
        except AttributeError:
            users_without_employees.append(user)
    
    if users_without_employees:
        print("❌ Users without Employee records:")
        for user in users_without_employees:
            print(f"   - {user.username}")
        print("   Create Employee records for these users!")
    
    # Check for inactive employees
    inactive_employees = Employee.objects.exclude(status='Active')
    if inactive_employees.exists():
        print("⚠️  Inactive employees found:")
        for emp in inactive_employees:
            print(f"   - {emp.user.username}: {emp.status}")
        print("   Set status to 'Active' for these employees to allow login!")
    
    if not users_without_employees and not inactive_employees.exists():
        print("✅ Authentication setup looks good!")
        print("   All users have active employee records.")

    # Check employer isolation for other models
    print("\n=== Employer Isolation Check ===")

    # Check JobTemplate
    templates_without_employer = JobTemplate.objects.filter(employer_id__isnull=True).count()
    total_templates = JobTemplate.objects.count()
    print(f"JobTemplate: {total_templates} total, {templates_without_employer} missing employer_id")

    # Check TalentPool
    talents_without_employer = TalentPool.objects.filter(employer_id__isnull=True).count()
    total_talents = TalentPool.objects.count()
    print(f"TalentPool: {total_talents} total, {talents_without_employer} missing employer_id")

    # Check Invitation
    invitations_without_employer = Invitation.objects.filter(employer_id__isnull=True).count()
    total_invitations = Invitation.objects.count()
    print(f"Invitation: {total_invitations} total, {invitations_without_employer} missing employer_id")

    if templates_without_employer + talents_without_employer + invitations_without_employer > 0:
        print("\n⚠️  Some records are missing employer isolation!")
        print("   Run: python manage.py migrate_employer_isolation --dry-run")
        print("   Then: python manage.py migrate_employer_isolation")
    else:
        print("\n✅ All models have proper employer isolation!")

if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        print(f"Error: {e}")
        print("Make sure you're running this from the project directory and Django is properly configured.")
