from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.contrib.auth.decorators import login_required
from django.views.decorators.csrf import csrf_exempt
from .services import analyze_cv, llm_session
from feed.models import Application, Vacancy, ApplicationCvText
from feed.decorators import permission_required
import logging
import json
from django.utils import translation

logger = logging.getLogger(__name__)

@require_POST
@permission_required('manage_candidates')
def analyze_application(request, application_id):
    try:
        print(f"Received request for application ID: {application_id}")
        # Get application and related data - only fetch necessary fields
        application = Application.objects.select_related('vacancy_id') \
            .only('application_id', 'score', 'vacancy_id__vacancy_job_description') \
            .get(pk=application_id)
        vacancy = application.vacancy_id

        # Get the job description
        job_description = vacancy.vacancy_job_description
        if not job_description:
            print(f"Job description not found for vacancy ID: {application_id}")
            return JsonResponse({
                "success": False,
                "message": "Job description not found for this vacancy."
            }, status=404)
        
        # Get the CV text from ApplicationCvText model - only fetch necessary fields
        try:
            cv_text_obj = ApplicationCvText.objects.only(
                'cv_text',
                'is_cv_analyzed',
                'ai_analysis_result'
            ).get(application_id=application)
            cv_text = cv_text_obj.cv_text
            
            if not cv_text or len(cv_text) < 20:
                print(f"CV text is empty or too short for application ID: {application_id}")
                return JsonResponse({
                    "success": False,
                    "message": "CV text is empty or too short for this application."
                }, status=404)
                
        except ApplicationCvText.DoesNotExist:
            print(f"CV text not found for application ID: {application_id}")
            return JsonResponse({
                "success": False,
                "message": "CV text not found for this application."
            }, status=404)
        
        # Check if the CV has already been analyzed successfully (score != -1)
        if (cv_text_obj.is_cv_analyzed and cv_text_obj.ai_analysis_result and
            cv_text_obj.ai_analysis_result.get('score', -1) != -1):
            print("CV is already analyzed successfully, using cached result.")
            logger.info(f"Using cached analysis for application {application_id}")
            analysis_result = cv_text_obj.ai_analysis_result
        else:
            if cv_text_obj.ai_analysis_result and cv_text_obj.ai_analysis_result.get('score', -1) == -1:
                print("Previous analysis failed (score -1), retrying analysis.")
            else:
                print("CV is not analyzed yet, performing analysis.")

            # Call the analyze_cv function to analyze the CV
            analysis_result = analyze_cv(cv_text, job_description)

            # Only mark as analyzed if the analysis was successful (score != -1)
            cv_text_obj.ai_analysis_result = analysis_result
            if analysis_result.get('score', -1) != -1:
                cv_text_obj.is_cv_analyzed = True
                print("Analysis successful, marking as analyzed.")
            else:
                cv_text_obj.is_cv_analyzed = False
                print("Analysis failed (score -1), keeping as not analyzed for retry.")
            cv_text_obj.save(update_fields=['ai_analysis_result', 'is_cv_analyzed'])

            # Update application with extracted CV information
            if analysis_result.get('score', -1) != -1:  # Only update if analysis was successful
                update_fields = {'score': analysis_result.get("score", -1)}
                
                # Extract and update total experience years
                total_experience = analysis_result.get('total_experience', '')
                if total_experience:
                    try:
                        # Parse experience like "5 years, 3 months" or "3 years" 
                        import re
                        years_match = re.search(r'(\d+)\s*years?', total_experience.lower())
                        months_match = re.search(r'(\d+)\s*months?', total_experience.lower())
                        
                        years = int(years_match.group(1)) if years_match else 0
                        months = int(months_match.group(1)) if months_match else 0
                        total_years = years + (months / 12.0)
                        
                        update_fields['total_exp_years'] = round(total_years, 1)
                    except (ValueError, AttributeError):
                        # If parsing fails, keep the default value
                        pass
                
                # Update current employer
                latest_employer = analysis_result.get('latest_employer', '').strip()
                if latest_employer and latest_employer.lower() not in ['not specified', 'unknown', '']:
                    update_fields['current_employer'] = latest_employer
                
                # Update current position
                latest_role = analysis_result.get('latest_role', '').strip()
                if latest_role and latest_role.lower() not in ['not specified', 'unknown', '']:
                    update_fields['current_position'] = latest_role
                
                # Update education level
                latest_degree = analysis_result.get('latest_degree', '').strip()
                if latest_degree and latest_degree.lower() not in ['not specified', 'unknown', '']:
                    update_fields['education_level'] = latest_degree
                
                # Update the application with all fields at once
                Application.objects.filter(pk=application_id).update(**update_fields)
            else:
                # If analysis failed, only update the score
                Application.objects.filter(pk=application_id) \
                    .update(score=analysis_result.get("score", -1))
            
            logger.info(f"Analysis saved for application {application_id}")
            print(f"Analysis result: {analysis_result}")
        print("Response is being sent back to the client.")
        return JsonResponse({
            "success": True,
            "analysis": analysis_result
        })
    except Application.DoesNotExist:
        print(f"Application not found for ID: {application_id}")
        return JsonResponse({
            "success": False,
            "message": "Application not found."
        }, status=404)
    except Exception as e:
        print(f"Error in analyze_application view: {str(e)}")
        logger.error(f"Error in analyze_application view: {str(e)}")
        return JsonResponse({
            "success": False,
            "message": f"An error occurred: {str(e)}"
        }, status=500)

@require_POST
@permission_required('create_jobs')
def generate_job_description(request):
    try:
        job_summary = json.loads(request.body)
        current_language = translation.get_language_info(translation.get_language())["name"]
        prompt = f"""
        You are a professional job description writer. Write a job description for a {job_summary['roleTitle']} position with the following information:

        - Office Location: {job_summary['officeLocation']}
        - Work Schedule: {job_summary['workSchedule']}
        - Office Schedule: {job_summary['officeSchedule']}
        - Skills: {job_summary['skills']}
        - Salary Range: {job_summary['salaryMin']} to {job_summary['salaryMax']} in {job_summary['salaryCurrency']}
        - Benefits: {job_summary['benefits']}

        Return ONLY a valid JSON object with these exact fields in {current_language} language:
        {{
            "job_description": "<Full job description in rich text format in {current_language} language>"
        }}
        """
        # Start LLM session
        job_description = llm_session(prompt)
        return JsonResponse({
            "success": True,
            "job_description": job_description['job_description']
        })
    except Exception as e:
        logger.error(f"Error in generate_job_description view: {str(e)}")
        return JsonResponse({
            "success": False,
            "message": f"An error occurred: {str(e)}"
        }, status=500)

@require_POST
@permission_required('create_jobs')
def suggest_job_requirements(request):
    try:
        job_data = json.loads(request.body)

        # Extract job information
        role_title = job_data.get('roleTitle', '')
        location = job_data.get('location', '')
        department = job_data.get('department', '')

        if not role_title or not location:
            return JsonResponse({
                "success": False,
                "message": "Role title and location are required for AI suggestions"
            }, status=400)

        # Create a prompt for AI to suggest skills, salary, and benefits
        prompt = f"""
        Based on the following job information, suggest appropriate skills, salary range, and benefits:

        Job Title: {role_title}
        Location: {location}
        Department: {department if department else 'Not specified'}

        Please provide suggestions in the following JSON format:
        {{
            "skills": ["skill1", "skill2", "skill3", ...],
            "salary": {{
                "min": minimum_salary_number,
                "max": maximum_salary_number,
                "currency": "currency_code"
            }},
            "benefits": ["benefit1", "benefit2", "benefit3", ...]
        }}

        Consider:
        - Skills should be relevant to the job title and industry
        - Salary should be appropriate for the location and role level
        - Benefits should be common and attractive for this type of position
        - Provide 5-8 skills, 3-5 benefits
        - Use appropriate currency for the location (USD for US, EUR for Europe, etc.)
        """

        # Call the LLM service with the prompt
        ai_response = llm_session(prompt)
        print(f"AI response: {ai_response}")
        # Parse the AI response to extract structured data
        # This is a simplified version - you might want to add more robust parsing
        try:
            # Assuming the AI returns JSON in the response
            import re
            json_match = re.search(r'\{.*\}', ai_response.get('job_description', ''), re.DOTALL)
            if json_match:
                suggestions = json.loads(json_match.group())
            else:
                # Fallback suggestions if AI doesn't return proper JSON
                suggestions = {
                    "skills": ["Communication", "Problem Solving", "Teamwork", "Time Management"],
                    "salary": {
                        "min": 50000,
                        "max": 80000,
                        "currency": "USD"
                    },
                    "benefits": ["Health Insurance", "Paid Time Off", "Professional Development"]
                }
        except (json.JSONDecodeError, KeyError):
            # Fallback suggestions
            suggestions = {
                "skills": ["Communication", "Problem Solving", "Teamwork", "Time Management"],
                "salary": {
                    "min": 50000,
                    "max": 80000,
                    "currency": "USD"
                },
                "benefits": ["Health Insurance", "Paid Time Off", "Professional Development"]
            }

        return JsonResponse({
            "success": True,
            **suggestions
        })

    except Exception as e:
        logger.error(f"Error in suggest_job_requirements view: {str(e)}")
        return JsonResponse({
            "success": False,
            "message": f"An error occurred: {str(e)}"
        }, status=500)