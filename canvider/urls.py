"""
URL configuration for canvider project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.http import HttpResponseNotFound
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.shortcuts import render

def custom_404_view(request, exception):
    return render(request, '404.html', status=404)

handler404 = custom_404_view

urlpatterns = [
    path('i18n/', include('django.conf.urls.i18n')),
    path('', include('feed.urls')),
    path('ai/', include('canviderAi.urls')),
]

# Catch-all pattern for undefined URLs - this will work in DEBUG mode too
urlpatterns += [
    path('<path:undefined_path>', lambda request, undefined_path: render(request, '404.html', status=404)),
]

# Only enable admin in development
if settings.DEBUG:
    from django.contrib import admin
    urlpatterns += [path('admin/', lambda request: HttpResponseNotFound())]
    # for local development comment out the line above and uncomment the line below
    # urlpatterns += [path('admin/', admin.site.urls)]

# Serve static files during development
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
