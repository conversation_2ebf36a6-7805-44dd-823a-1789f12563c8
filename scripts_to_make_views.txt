create or replace view day_aggregated_applicant_states as (WITH
  date_series AS (
    SELECT
      (
        generate_series(
          date_trunc(
            'day'::text,
            min(feed_applicationstate.state_started_at)
          ),
          date_trunc(
            'day'::text,
            max(feed_applicationstate.state_started_at)
          ),
          '1 day'::interval
        )
      )::date AS day
    FROM
      feed_applicationstate
  ),
  application_states AS (
    SELECT
      sc.application_id,
      d.day,
      last_value(sc.state_name) OVER (
        PARTITION BY
          sc.application_id,
          (date_trunc('day'::text, sc.state_started_at))
        ORDER BY
          sc.state_started_at ROWS BETWEEN UNBOUNDED PRECEDING
          AND UNBOUNDED FOLLOWING
      ) AS state_name
    FROM
      (
        feed_applicationstate sc
        JOIN date_series d ON (
          (
            date_trunc('day'::text, sc.state_started_at) <= d.day
          )
        )
      )
  ),
  daily_counts AS (
    SELECT
      application_states.day,
      application_states.state_name,
      count(DISTINCT application_states.application_id) AS count
    FROM
      application_states
    GROUP BY
      application_states.day,
      application_states.state_name
  )
SELECT
  day,
  state_name,
  count
FROM
  daily_counts
ORDER BY
  day,
  state_name);

create or replace view all_job_locations as (SELECT DISTINCT
  vacancy_city,
  vacancy_country
FROM
  feed_vacancy);

create or replace view employer_cards as (SELECT
  e.employer_id,
  e.employer_logo_url,
  e.employer_name,
  e.employer_industry,
  e.employer_headcount,
  split_part((e.office_locations)::text, '|'::text, 1) AS headquarter,
  COALESCE(j.open_positions, (0)::bigint) AS open_positions
FROM
  (
    feed_employer e
    LEFT JOIN (
      SELECT
        feed_vacancy.employer_id,
        count(feed_vacancy.vacancy_id) AS open_positions
      FROM
        feed_vacancy
      WHERE
        (
          (feed_vacancy.vacancy_status)::text = 'Active'::text
        )
      GROUP BY
        feed_vacancy.employer_id
    ) j ON (((e.employer_id)::text = (j.employer_id)::text))
  )
);

create or replace view highlighted_jobs as (SELECT
  a.vacancy_id,
  a.vacancy_status,
  a.vacancy_title,
  a.salary_min,
  a.salary_max,
  a.salary_currency,
  a.vacancy_city,
  a.office_schedule,
  a.vacancy_creation_date,
  a.vacancy_country,
  b.employer_logo_url,
  CASE
    WHEN (
      age (now(), a.vacancy_creation_date) < '00:01:00'::interval
    ) THEN 'Just now'::text
    WHEN (
      age (now(), a.vacancy_creation_date) < '01:00:00'::interval
    ) THEN concat(
      (
        EXTRACT(
          minute
          FROM
            age (now(), a.vacancy_creation_date)
        )
      )::integer,
      ' minutes ago'
    )
    WHEN (
      age (now(), a.vacancy_creation_date) < '1 day'::interval
    ) THEN concat(
      (
        EXTRACT(
          hour
          FROM
            age (now(), a.vacancy_creation_date)
        )
      )::integer,
      ' hours ago'
    )
    ELSE concat(
      (
        EXTRACT(
          day
          FROM
            age (now(), a.vacancy_creation_date)
        )
      )::integer,
      ' days ago'
    )
  END AS vacancy_age,
  b.employer_name,
  b.employer_industry
FROM
  (
    feed_vacancy a
    JOIN feed_employer b ON (((a.employer_id)::text = (b.employer_id)::text))
  )
ORDER BY
  a.vacancy_creation_date);

create or replace view vacancies as (SELECT
  a.vacancy_id,
  a.vacancy_status,
  a.vacancy_title,
  a.employer_id,
  b.employer_name,
  a.vacancy_city,
  a.vacancy_country,
  a.office_schedule,
  a.salary_min,
  a.salary_max,
  a.salary_currency,
  a.vacancy_job_description,
  a.jobtags,
  b.employer_logo_url,
  b.employer_banner_url
FROM
  (
    feed_vacancy a
    JOIN feed_employer b ON (((a.employer_id)::text = (b.employer_id)::text))
  )
ORDER BY
  a.vacancy_id);
