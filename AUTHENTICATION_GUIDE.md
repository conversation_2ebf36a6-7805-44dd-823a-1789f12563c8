# Authentication & Data Isolation System

## Overview
This system provides secure multi-tenant authentication with automatic data isolation for the Canvider application.

## Key Features

### 🔐 **Automatic Authentication Redirects**
- **Unauthenticated users** → Automatically redirected to `/signin/`
- **Authenticated users visiting signin** → Automatically redirected to `/feed/`
- **Successful login** → Redirected to `/feed/` or the originally requested page

### 🏢 **Multi-Tenant Data Isolation**
- Each employee can only see data from their own employer
- Automatic filtering at the middleware level
- No cross-employer data leakage

### 👤 **User Model Design**
- Uses Django's built-in `User` model with `OneToOneField` to `Employee`
- Maintains all Django authentication features
- Clean separation of authentication and business logic

## How It Works

### 1. **Middleware (`EmployerIsolationMiddleware`)**
```python
# Automatically handles:
- Authentication checks for all protected URLs
- Employee record validation
- Employer ID injection into requests
- Inactive account detection
```

### 2. **Authentication Flow**
```
User visits any URL
    ↓
Is user authenticated?
    ↓ No → Redirect to /signin/?next=original_url
    ↓ Yes
Does user have active Employee record?
    ↓ No → Logout + Redirect to /signin/ with error
    ↓ Yes
Add employer_id to request → Continue to view
```

### 3. **Data Filtering**
```python
# In views, data is automatically filtered:
request.employer_id  # Available in all views
request.employee     # Current employee object

# Example usage:
applications = Application.objects.filter(
    vacancy_id__employer_id=request.employer_id
)
```

## URL Access Control

### **Public URLs** (No authentication required)
- `/signin/`
- `/signout/`
- `/register/`
- `/static/`
- `/media/`
- `/admin/`

### **Protected URLs** (Authentication required)
- `/` (feed)
- `/jobs/`
- `/people/`
- `/create/`
- All other application URLs

## Testing the System

### **Test Authentication Status**
Visit `/test-auth/` to see:
- Authentication status
- Employee information
- Employer isolation data

### **Test Redirect Flow**
1. **Logout** → Visit any protected URL → Should redirect to signin
2. **Login** → Should redirect to feed or originally requested page
3. **Visit signin while logged in** → Should redirect to feed

## Security Features

### ✅ **Session Management**
- "Remember me" option for extended sessions
- Automatic session expiry on browser close (if not remembered)
- Session validation on each request

### ✅ **Employee Status Validation**
- Only "Active" employees can access the system
- Inactive employees are automatically logged out
- Proper error messages for account issues

### ✅ **Data Isolation**
- Employer A employees cannot see Employer B data
- Automatic filtering at database level
- No manual filtering required in views

## Usage Examples

### **In Views**
```python
def my_view(request):
    # No decorators needed - middleware handles authentication
    # request.employer_id and request.employee are automatically available
    
    vacancies = Vacancy.objects.filter(employer_id=request.employer_id)
    return render(request, 'template.html', {'vacancies': vacancies})
```

### **In Templates**
```html
<!-- User info automatically available -->
{% if request.user.is_authenticated %}
    Welcome {{ request.user.first_name }}!
    Company: {{ request.user.employee.employer_id.employer_name }}
    Role: {{ request.user.employee.role }}
{% endif %}
```

## Troubleshooting

### **Common Issues**

1. **"Account not properly configured"**
   - User exists but no Employee record
   - Solution: Create Employee record for the user

2. **"Account is inactive"**
   - Employee.status is not "Active"
   - Solution: Update employee status to "Active"

3. **Redirect loops**
   - Check middleware URL exemptions
   - Ensure signin URL is in public_urls list

### **Debug Information**
- Visit `/test-auth/` for detailed authentication status
- Check Django messages for error details
- Monitor server logs for middleware activity

## Migration Notes

### **From Previous System**
- No database changes required
- Existing User and Employee models work as-is
- Add middleware to settings.py
- Update views to use request.employer_id instead of manual filtering

### **Benefits Over AbstractUser**
- ✅ No complex migrations
- ✅ Keeps Django's built-in features
- ✅ Easier maintenance
- ✅ Better separation of concerns
