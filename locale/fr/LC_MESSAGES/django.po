# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-23 20:56+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: French <<EMAIL>>\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: feed/models.py:94
msgid "Active"
msgstr "Actif"

#: feed/models.py:95
msgid "Draft"
msgstr "Brouillon"

#: feed/models.py:96
msgid "Closed"
msgstr "Fermé"

#: feed/models.py:97
msgid "On-Hold"
msgstr "En attente"

#: feed/models.py:98
msgid "Archived"
msgstr "Archivé"

#: feed/models.py:99
msgid "Reviewing"
msgstr "En revue"

#: feed/models.py:100
msgid "Deleted"
msgstr "Supprimé"

#: feed/models.py:298 templates/applicant_dev.html:1098
msgid "New"
msgstr "Nouveau"

#: feed/models.py:299 templates/applicant_dev.html:1099
msgid "Review #1"
msgstr "Revue #1"

#: feed/models.py:300 templates/applicant_dev.html:1100
msgid "Review #2"
msgstr "Revue #2"

#: feed/models.py:301 templates/applicant_dev.html:1101
msgid "Review #3"
msgstr "Revue #3"

#: feed/models.py:302 templates/applicant_dev.html:1102
msgid "Review #4"
msgstr "Revue #4"

#: feed/models.py:303 templates/applicant_dev.html:1103
msgid "Review #5"
msgstr "Revue #5"

#: feed/models.py:304 templates/applicant_dev.html:1104
msgid "Ready for Decision"
msgstr "Prêt pour décision"

#: feed/models.py:305 templates/applicant_dev.html:1105
msgid "Eliminated"
msgstr "Éliminé"

#: feed/models.py:306 templates/applicant_dev.html:1106
msgid "Offer Made"
msgstr "Offre faite"

#: feed/models.py:307 templates/applicant_dev.html:1107
msgid "Candidate Accepted"
msgstr "Candidat accepté"

#: feed/models.py:308 templates/jobs.html:142
msgid "Hired"
msgstr "Embauché"

#: feed/models.py:309 templates/applicant_dev.html:1108
msgid "Candidate Rejected"
msgstr "Candidat refusé"

#: feed/models.py:348
msgid "Phone Call"
msgstr "Appel téléphonique"

#: feed/models.py:349
msgid "Video Call"
msgstr "Appel vidéo"

#: feed/models.py:350
msgid "Online Interview"
msgstr "Entretien en ligne"

#: feed/models.py:351
msgid "Technical Assessment"
msgstr "Évaluation technique"

#: feed/models.py:352
msgid "Final Interview"
msgstr "Entretien final"

#: feed/models.py:353
msgid "Face to Face Interview"
msgstr "Entretien en personne"

#: feed/models.py:354
msgid "Office Visit"
msgstr "Visite de bureau"

#: feed/models.py:355 templates/workloupe_platform.html:107
msgid "Other"
msgstr "Autre"

#: feed/views.py:154
#, python-format
msgid "<strong>%(name)s</strong> applied for <strong>%(position)s</strong>"
msgstr "<strong>%(name)s</strong> a postulé pour <strong>%(position)s</strong>"

#: feed/views.py:195
#, python-format
msgid ""
"<strong>%(name)s</strong> moved to <strong>%(state)s</strong> for "
"<strong>%(position)s</strong>"
msgstr "<strong>%(name)s</strong> déplacé à <strong>%(state)s</strong> pour <strong>%(position)s</strong>"

#: feed/views.py:225
#, python-format
msgid "A new vacancy <strong>%(vacancy_title)s</strong> is published"
msgstr "Un nouveau poste <strong>%(vacancy_title)s</strong> est publié"

#: feed/views.py:249
msgid "New comment on application of"
msgstr "Nouveau commentaire sur la candidature de"

#: feed/views.py:251
msgid "New comment on application ID"
msgstr "Nouveau commentaire sur l'ID de candidature"

#: feed/views.py:267
msgid "now"
msgstr "maintenant"

#: feed/views.py:270
msgid "1 minute ago"
msgstr "il y a 1 minute"

#: feed/views.py:273
msgid "minutes ago"
msgstr "minutes"

#: feed/views.py:276
msgid "1 hour ago"
msgstr "il y a 1 heure"

#: feed/views.py:280
msgid "hours ago"
msgstr "heures"

#: feed/views.py:283
msgid "yesterday"
msgstr "hier"

#: feed/views.py:287
msgid "days ago"
msgstr "jours"

#: feed/views.py:290
msgid "last week"
msgstr "la semaine dernière"

#: feed/views.py:294
msgid "weeks ago"
msgstr "semaines"

#: feed/views.py:297
msgid "last month"
msgstr "le mois dernier"

#: feed/views.py:301
msgid "months ago"
msgstr "mois"

#: feed/views.py:306
msgid "last year"
msgstr "l'année dernière"

#: feed/views.py:308
msgid "years ago"
msgstr "ans"

#: feed/views.py:1038
msgid "Profile photo changed successfully!"
msgstr "Photo de profil modifiée avec succès !"

#: feed/views.py:1043
msgid "Please select a photo."
msgstr "Veuillez sélectionner une photo."

#: feed/views.py:1852
#, python-format
msgid "Language changed to %(language)s"
msgstr "Langue changée en %(language)s"

#: feed/views.py:1856
msgid "Invalid language selection"
msgstr "Sélection de langue invalide"

#: feed/views.py:1885 templates/feed.html:17
msgid "Dashboard"
msgstr "Tableau de bord"

#: feed/views.py:2075
msgid "Invitation mail sent successfully!"
msgstr "E-mail d'invitation envoyé avec succès !"

#: feed/views.py:2078 feed/views.py:2221
msgid "Failed to send the invitation. Please check the form."
msgstr "Échec de l'envoi de l'invitation. Veuillez vérifier le formulaire."

#: feed/views.py:2105
msgid "Passwords do not match."
msgstr "Les mots de passe ne correspondent pas."

#: feed/views.py:2136
msgid "No employer found to associate with this account."
msgstr "Aucun employeur trouvé à associer à ce compte."

#: feed/views.py:2146
msgid "Registration completed successfully! You can now log in."
msgstr "Inscription terminée avec succès ! Vous pouvez maintenant vous connecter."

#: feed/views.py:2150
#, python-format
msgid "Error creating account: %(error)s"
msgstr "Erreur lors de la création du compte : %(error)s"

#: feed/views.py:2176
msgid "Access denied."
msgstr "Accès refusé."

#: feed/views.py:2203
msgid "Invitation sent successfully!"
msgstr "Invitation envoyée avec succès !"

#: feed/views.py:2232
msgid "User removed successfully!"
msgstr "Utilisateur supprimé avec succès !"

#: feed/views.py:2245
msgid "User status changed successfully!"
msgstr "Statut utilisateur modifié avec succès !"

#: feed/views.py:2397
msgid "Talent request sent successfully! Our team will get back to you soon."
msgstr "Demande de talent envoyée avec succès ! Notre équipe vous contactera bientôt."

#: feed/views.py:2401
msgid "Invalid request method."
msgstr "Méthode de requête invalide."

#: feed/views.py:4646
msgid "Image URL and employer ID are required"
msgstr "L'URL de l'image et l'ID employeur sont requis"

#: feed/views.py:4678
#, python-format
msgid "Failed to remove image: %(error)s"
msgstr "Échec de la suppression de l'image : %(error)s"

#: feed/views.py:4683
msgid "Invalid request method"
msgstr "Méthode de requête invalide"

#: templates/applicant_dev.html:30
msgid "Applied for:"
msgstr "Postulé pour :"

#: templates/applicant_dev.html:89 templates/applicant_dev.html:870
msgid "Schedule Interview"
msgstr "Planifier un entretien"

#: templates/applicant_dev.html:99
msgid "Change State"
msgstr "Changer l'état"

#: templates/applicant_dev.html:114
msgid "Dashboard & AI"
msgstr "Tableau de bord & IA"

#: templates/applicant_dev.html:123
msgid "Candidate Background"
msgstr "Contexte du candidat"

#: templates/applicant_dev.html:132 templates/applicant_dev.html:565
#: templates/applicant_dev.html:583
msgid "Resume"
msgstr "CV"

#: templates/applicant_dev.html:141
msgid "Journey"
msgstr "Parcours"

#: templates/applicant_dev.html:150 templates/applicant_dev.html:785
msgid "Internal Comments"
msgstr "Commentaires internes"

#: templates/applicant_dev.html:159 templates/applicant_dev.html:685
msgid "Emails"
msgstr "E-mails"

#: templates/applicant_dev.html:167 templates/applicant_dev.html:854
msgid "Job Details"
msgstr "Détails du poste"

#: templates/applicant_dev.html:191
msgid "Profile Match Analysis"
msgstr "Analyse de correspondance du profil"

#: templates/applicant_dev.html:199
msgid "Key Highlights"
msgstr "Points clés"

#: templates/applicant_dev.html:225
msgid "AI analysis will provide candidate highlights."
msgstr "L'analyse IA fournira les points forts du candidat."

#: templates/applicant_dev.html:235
msgid "Areas for Improvement"
msgstr "Domaines d'amélioration"

#: templates/applicant_dev.html:272
msgid "Candidate Summary"
msgstr "Résumé du candidat"

#: templates/applicant_dev.html:321
msgid ""
"Based on the AI analysis, when the resume is compared to the job "
"requirements,"
msgstr "Sur la base de l'analyse IA, lorsque le CV est comparé aux exigences du poste,"

#: templates/applicant_dev.html:323
msgid "This candidate is an"
msgstr "Ce candidat est un"

#: templates/applicant_dev.html:324
msgid "excellent match"
msgstr "excellente correspondance"

#: templates/applicant_dev.html:326 templates/applicant_dev.html:329
#: templates/applicant_dev.html:332
msgid "This candidate is a"
msgstr "Ce candidat est une"

#: templates/applicant_dev.html:327
msgid "good match"
msgstr "bonne correspondance"

#: templates/applicant_dev.html:330
msgid "fair match"
msgstr "correspondance moyenne"

#: templates/applicant_dev.html:333
msgid "weak match"
msgstr "faible correspondance"

#: templates/applicant_dev.html:336
msgid "Analyze the CV with AI to see match details."
msgstr "Analysez le CV avec l'IA pour voir les détails de correspondance."

#: templates/applicant_dev.html:363
msgid "AI Analysis Available"
msgstr "Analyse IA disponible"

#: templates/applicant_dev.html:366
msgid "Leverage AI to analyze this candidate's CV against the job description."
msgstr "Utilisez l'IA pour analyser le CV de ce candidat par rapport à la description de poste."

#: templates/applicant_dev.html:387
msgid "Analyze with CanviderAI"
msgstr "Analyser avec CanviderAI"

#: templates/applicant_dev.html:402
msgid "Analyzing CV..."
msgstr "Analyse du CV..."

#: templates/applicant_dev.html:403
msgid "This may take a moment"
msgstr "Cela peut prendre un moment"

#: templates/applicant_dev.html:428 templates/applicant_dev.html:449
msgid "Analysis complete"
msgstr "Analyse terminée"

#: templates/applicant_dev.html:460
msgid "Candidate Facts"
msgstr "Faits sur le candidat"

#: templates/applicant_dev.html:467
msgid "Applied Position"
msgstr "Poste postulé"

#: templates/applicant_dev.html:473
msgid "Candidate's Address"
msgstr "Adresse du candidat"

#: templates/applicant_dev.html:476 templates/applicant_dev.html:499
#: templates/applicant_dev.html:506 templates/applicant_dev.html:529
#: templates/applicant_dev.html:536 templates/people.html:119
#: templates/people.html:126 templates/people.html:133
msgid "Not analyzed"
msgstr "Non analysé"

#: templates/applicant_dev.html:480 templates/people.html:61
msgid "Application Date"
msgstr "Date de candidature"

#: templates/applicant_dev.html:486
msgid "Application Portal"
msgstr "Portail de candidature"

#: templates/applicant_dev.html:496
msgid "Latest/Current Position"
msgstr "Dernier/Actuel poste"

#: templates/applicant_dev.html:503
msgid "Latest/Current Employer"
msgstr "Dernier/Actuel employeur"

#: templates/applicant_dev.html:510 templates/jobs.html:69
#: templates/manage_permissions.html:117 templates/manage_permissions.html:264
#: templates/people.html:41 templates/people.html:89
#: templates/published_job_details.html:279
msgid "Status"
msgstr "Statut"

#: templates/applicant_dev.html:516
msgid "Application ID"
msgstr "ID de candidature"

#: templates/applicant_dev.html:526
msgid "Total Experience"
msgstr "Expérience totale"

#: templates/applicant_dev.html:533
msgid "Education Level"
msgstr "Niveau d'éducation"

#: templates/applicant_dev.html:540
msgid "Notice Period"
msgstr "Délai de préavis"

#: templates/applicant_dev.html:546
msgid "Last Communication Date"
msgstr "Date de dernière communication"

#: templates/applicant_dev.html:551 templates/applicant_dev.html:722
msgid "No emails found."
msgstr "Aucun e-mail trouvé."

#: templates/applicant_dev.html:570
msgid "Uploaded on"
msgstr "Téléchargé le"

#: templates/applicant_dev.html:588
msgid "Open in New Tab"
msgstr "Ouvrir dans un nouvel onglet"

#: templates/applicant_dev.html:593
msgid "Download"
msgstr "Télécharger"

#: templates/applicant_dev.html:619
msgid "PDF Preview Not Available"
msgstr "Aperçu PDF non disponible"

#: templates/applicant_dev.html:620
msgid ""
"Your browser doesn't support PDF preview. Please download the file to view "
"it."
msgstr "Votre navigateur ne prend pas en charge l'aperçu PDF. Téléchargez le fichier pour le visualiser."

#: templates/applicant_dev.html:627
msgid "Download PDF"
msgstr "Télécharger PDF"

#: templates/applicant_dev.html:644
msgid "Application Stages"
msgstr "Étapes de candidature"

#: templates/applicant_dev.html:661
msgid "Started on:"
msgstr "Commencé le :"

#: templates/applicant_dev.html:673
msgid "No stages available for this application."
msgstr "Aucune étape disponible pour cette candidature."

#: templates/applicant_dev.html:691
msgid "Email History"
msgstr "Historique des e-mails"

#: templates/applicant_dev.html:701
msgid "From:"
msgstr "De :"

#: templates/applicant_dev.html:702
msgid "To:"
msgstr "À :"

#: templates/applicant_dev.html:705 templates/applicant_dev.html:1145
msgid "Subject:"
msgstr "Sujet :"

#: templates/applicant_dev.html:729 templates/applicant_dev.html:775
msgid "Send Email"
msgstr "Envoyer un e-mail"

#: templates/applicant_dev.html:744
msgid "Subject"
msgstr "Sujet"

#: templates/applicant_dev.html:754 templates/published_job_details.html:443
msgid "Email Body"
msgstr "Corps de l'e-mail"

#: templates/applicant_dev.html:797
msgid "Add a comment"
msgstr "Ajouter un commentaire"

#: templates/applicant_dev.html:803
msgid "Add your comment here..."
msgstr "Ajoutez votre commentaire ici..."

#: templates/applicant_dev.html:808
msgid "Post Comment"
msgstr "Publier le commentaire"

#: templates/applicant_dev.html:841
msgid "No comments yet. Be the first to comment!"
msgstr "Aucun commentaire pour l'instant. Soyez le premier !"

#: templates/applicant_dev.html:876 templates/feed.html:302
msgid "Event Title"
msgstr "Titre de l'événement"

#: templates/applicant_dev.html:887 templates/feed.html:312
msgid "Event Type"
msgstr "Type d'événement"

#: templates/applicant_dev.html:893 templates/feed.html:318
msgid "Select an event type"
msgstr "Sélectionner un type d'événement"

#: templates/applicant_dev.html:901 templates/feed.html:326
#: templates/manage_permissions.html:49
msgid "Recruiters"
msgstr "Recruteurs"

#: templates/applicant_dev.html:906 templates/feed.html:331
msgid "Select one or many recruiters"
msgstr "Sélectionner un ou plusieurs recruteurs"

#: templates/applicant_dev.html:916 templates/feed.html:384
#: templates/people.html:31 templates/people.html:88 templates/profile.html:74
#: templates/profile.html:115
msgid "Position"
msgstr "Poste"

#: templates/applicant_dev.html:933 templates/feed.html:406
msgid "Candidate"
msgstr "Candidat"

#: templates/applicant_dev.html:949
msgid "Date"
msgstr "Date"

#: templates/applicant_dev.html:958 templates/feed.html:420
msgid "Start Time"
msgstr "Heure de début"

#: templates/applicant_dev.html:964 templates/feed.html:425
msgid "End Time"
msgstr "Heure de fin"

#: templates/applicant_dev.html:971 templates/feed.html:431
msgid "Meeting Link"
msgstr "Lien de réunion"

#: templates/applicant_dev.html:984 templates/feed.html:445
msgid "Generate Mirotalk Link"
msgstr "Générer un lien Mirotalk"

#: templates/applicant_dev.html:999 templates/feed.html:460
msgid "Inform invitees by E-mail"
msgstr "Informer les invités par e-mail"

#: templates/applicant_dev.html:1004 templates/feed.html:465
msgid "Color"
msgstr "Couleur"

#: templates/applicant_dev.html:1006 templates/feed.html:467
msgid "Blue"
msgstr "Bleu"

#: templates/applicant_dev.html:1007 templates/feed.html:468
msgid "Light Blue"
msgstr "Bleu clair"

#: templates/applicant_dev.html:1008 templates/feed.html:469
msgid "Purple"
msgstr "Violet"

#: templates/applicant_dev.html:1009 templates/feed.html:470
msgid "Pink"
msgstr "Rose"

#: templates/applicant_dev.html:1016 templates/applicant_dev.html:1173
#: templates/create_job_template.html:205 templates/feed.html:476
#: templates/job_details.html:134 templates/manage_permissions.html:413
#: templates/manage_permissions.html:463 templates/profile.html:125
#: templates/profile.html:152 templates/profile.html:184
#: templates/published_job_details.html:380
#: templates/published_job_details.html:465
#: templates/published_job_details.html:686
msgid "Cancel"
msgstr "Annuler"

#: templates/applicant_dev.html:1019 templates/feed.html:479
msgid "Save Event"
msgstr "Enregistrer l'événement"

#: templates/applicant_dev.html:1078
msgid "Change Application Status"
msgstr "Changer le statut de candidature"

#: templates/applicant_dev.html:1096
msgid "New Status"
msgstr "Nouveau statut"

#: templates/applicant_dev.html:1112 templates/published_job_details.html:432
msgid "Internal Notes"
msgstr "Notes internes"

#: templates/applicant_dev.html:1112 templates/published_job_details.html:432
msgid "(visible only to recruiters)"
msgstr "(visible uniquement par les recruteurs)"

#: templates/applicant_dev.html:1126
msgid "Notify candidate about this status change via email"
msgstr "Notifier le candidat de ce changement de statut par e-mail"

#: templates/applicant_dev.html:1132
msgid "Email Message"
msgstr "Message e-mail"

#: templates/applicant_dev.html:1132
msgid "(will be included in the email to the candidate)"
msgstr "(sera inclus dans l'e-mail au candidat)"

#: templates/applicant_dev.html:1143
msgid "Email Preview"
msgstr "Aperçu de l'e-mail"

#: templates/applicant_dev.html:1145
msgid "Your application status has been updated"
msgstr "Votre statut de candidature a été mis à jour"

#: templates/applicant_dev.html:1175
msgid "Save Change"
msgstr "Enregistrer la modification"

#: templates/careers_page.html:10
msgid "Career Page Setup"
msgstr "Configuration de la page carrière"

#: templates/careers_page.html:11
msgid "Choose the integration method that best suits your needs"
msgstr "Choisissez la méthode d'intégration qui correspond le mieux à vos besoins"

#: templates/careers_page.html:23
msgid "RSS Feed Integration"
msgstr "Intégration de flux RSS"

#: templates/careers_page.html:24
msgid ""
"Already have your own careers page? Get our RSS feed to sync job listings"
msgstr "Vous avez déjà votre page carrière ? Obtenez notre flux RSS pour synchroniser les offres d'emploi"

#: templates/careers_page.html:26
msgid "Quick Setup"
msgstr "Configuration rapide"

#: templates/careers_page.html:27
msgid "Auto Sync"
msgstr "Synchronisation automatique"

#: templates/careers_page.html:32
msgid "Get RSS Feed"
msgstr "Obtenir le flux RSS"

#: templates/careers_page.html:40
msgid "Recommended"
msgstr "Recommandé"

#: templates/careers_page.html:45
msgid "Full HTML Page"
msgstr "Page HTML complète"

#: templates/careers_page.html:46
msgid "Let us manage your entire careers page with our professional template"
msgstr "Laissez-nous gérer toute votre page carrière avec notre modèle professionnel"

#: templates/careers_page.html:48
msgid "Professional"
msgstr "Professionnel"

#: templates/careers_page.html:49
msgid "Customizable"
msgstr "Personnalisable"

#: templates/careers_page.html:54
msgid "Create Page"
msgstr "Créer une page"

#: templates/careers_page.html:67
msgid "Workloupe Platform"
msgstr "Plateforme Workloupe"

#: templates/careers_page.html:68
msgid "Use our platform as your company's career page"
msgstr "Utilisez notre plateforme comme page carrière de votre entreprise"

#: templates/careers_page.html:70
msgid "Hosted"
msgstr "Hébergé"

#: templates/careers_page.html:71
msgid "Full Featured"
msgstr "Fonctionnalités complètes"

#: templates/careers_page.html:76
msgid "Setup Platform"
msgstr "Configurer la plateforme"

#: templates/careers_page.html:91
msgid "Your RSS Feed URL"
msgstr "URL de votre flux RSS"

#: templates/careers_page.html:98
msgid ""
"Use this RSS feed URL to automatically sync your job listings with your "
"existing careers page."
msgstr "Utilisez cette URL de flux RSS pour synchroniser automatiquement vos offres d'emploi avec votre page carrière existante."

#: templates/careers_page.html:101
msgid "RSS Feed URL"
msgstr "URL du flux RSS"

#: templates/careers_page.html:106 templates/published_job_details.html:516
#: templates/published_job_details.html:559
#: templates/published_job_details.html:585
#: templates/workloupe_platform.html:311
msgid "Copy"
msgstr "Copier"

#: templates/careers_page.html:111 templates/create_careers_widget.html:182
msgid "Integration Instructions:"
msgstr "Instructions d'intégration :"

#: templates/careers_page.html:113
msgid "Copy the RSS feed URL above"
msgstr "Copiez l'URL du flux RSS ci-dessus"

#: templates/careers_page.html:114
msgid "Add it to your website's RSS feed reader or job board integration"
msgstr "Ajoutez-la au lecteur RSS de votre site ou à l'intégration du tableau d'offres"

#: templates/careers_page.html:115
msgid "Your job listings will automatically sync"
msgstr "Vos offres d'emploi se synchroniseront automatiquement"

#: templates/careers_page.html:286 templates/create_careers_widget.html:716
#: templates/wordpress_integration.html:775
#: templates/workloupe_platform.html:1142
msgid "Copied!"
msgstr "Copié !"

#: templates/careers_page.html:297 templates/create_careers_widget.html:726
#: templates/wordpress_integration.html:785
msgid "Failed to copy. Please copy manually."
msgstr "Échec de la copie. Veuillez copier manuellement."

#: templates/create_careers_widget.html:13
msgid "Widget Builder"
msgstr "Constructeur de widget"

#: templates/create_careers_widget.html:14
msgid "Customize your careers widget"
msgstr "Personnalisez votre widget carrière"

#: templates/create_careers_widget.html:22
#: templates/wordpress_integration.html:45
msgid "Company Branding"
msgstr "Identité de l'entreprise"

#: templates/create_careers_widget.html:26
#: templates/wordpress_integration.html:49 templates/workloupe_platform.html:34
msgid "Company Name"
msgstr "Nom de l'entreprise"

#: templates/create_careers_widget.html:27
#: templates/wordpress_integration.html:50
msgid "Enter company name"
msgstr "Entrez le nom de l'entreprise"

#: templates/create_careers_widget.html:31
#: templates/wordpress_integration.html:54
msgid "Tagline"
msgstr "Slogan"

#: templates/create_careers_widget.html:32
#: templates/wordpress_integration.html:55
msgid "Enter company tagline"
msgstr "Entrez le slogan de l'entreprise"

#: templates/create_careers_widget.html:36
#: templates/workloupe_platform.html:175
msgid "Company Logo"
msgstr "Logo de l'entreprise"

#: templates/create_careers_widget.html:38
msgid "Recommended: 200x80px, PNG or JPG"
msgstr "Recommandé : 200x80px, PNG ou JPG"

#: templates/create_careers_widget.html:46
msgid "Design & Colors"
msgstr "Design & Couleurs"

#: templates/create_careers_widget.html:50
#: templates/wordpress_integration.html:67
msgid "Primary Color"
msgstr "Couleur principale"

#: templates/create_careers_widget.html:55
msgid "Background Color"
msgstr "Couleur d'arrière-plan"

#: templates/create_careers_widget.html:60
msgid "Text Color"
msgstr "Couleur du texte"

#: templates/create_careers_widget.html:65
msgid "Widget Style"
msgstr "Style du widget"

#: templates/create_careers_widget.html:67
#: templates/wordpress_integration.html:75
msgid "Modern"
msgstr "Moderne"

#: templates/create_careers_widget.html:68
#: templates/wordpress_integration.html:76
msgid "Classic"
msgstr "Classique"

#: templates/create_careers_widget.html:69
#: templates/wordpress_integration.html:77
msgid "Minimal"
msgstr "Minimaliste"

#: templates/create_careers_widget.html:78
#: templates/wordpress_integration.html:93
msgid "Content Settings"
msgstr "Paramètres de contenu"

#: templates/create_careers_widget.html:82
msgid "Max Jobs to Display"
msgstr "Nombre max d'offres à afficher"

#: templates/create_careers_widget.html:84
#: templates/create_careers_widget.html:85
#: templates/create_careers_widget.html:86 templates/jobs.html:186
#: templates/wordpress_integration.html:99
#: templates/wordpress_integration.html:100
#: templates/wordpress_integration.html:101
msgid "jobs"
msgstr "offres"

#: templates/create_careers_widget.html:87
#: templates/wordpress_integration.html:102
msgid "All jobs"
msgstr "Toutes les offres"

#: templates/create_careers_widget.html:94
msgid "Show Salary Information"
msgstr "Afficher les informations salariales"

#: templates/create_careers_widget.html:101
msgid "Show Job Location"
msgstr "Afficher le lieu de travail"

#: templates/create_careers_widget.html:108
msgid "Show Posted Date"
msgstr "Afficher la date de publication"

#: templates/create_careers_widget.html:117
msgid "Generate Widget Code"
msgstr "Générer le code du widget"

#: templates/create_careers_widget.html:126
msgid "Live Preview"
msgstr "Aperçu en direct"

#: templates/create_careers_widget.html:160
msgid "Your Widget Code"
msgstr "Votre code de widget"

#: templates/create_careers_widget.html:167
msgid ""
"Copy this code and paste it into your website where you want the careers "
"widget to appear."
msgstr "Copiez ce code et collez-le sur votre site où vous voulez que le widget carrière apparaisse."

#: templates/create_careers_widget.html:172
msgid "HTML Widget Code"
msgstr "Code HTML du widget"

#: templates/create_careers_widget.html:175
#: templates/wordpress_integration.html:246
msgid "Copy Code"
msgstr "Copier le code"

#: templates/create_careers_widget.html:184
msgid "Copy the HTML code above"
msgstr "Copiez le code HTML ci-dessus"

#: templates/create_careers_widget.html:185
msgid "Paste it into your website's HTML where you want the widget to appear"
msgstr "Collez-le dans le HTML de votre site où vous voulez que le widget apparaisse"

#: templates/create_careers_widget.html:186
msgid "The widget will automatically load your latest job postings"
msgstr "Le widget chargera automatiquement vos dernières offres d'emploi"

#: templates/create_careers_widget.html:187
msgid "The widget is responsive and will adapt to your website's layout"
msgstr "Le widget est réactif et s'adaptera à la mise en page de votre site"

#: templates/create_careers_widget.html:192
#: templates/wordpress_integration.html:253
#: templates/workloupe_platform.html:338
msgid "Close"
msgstr "Fermer"

#: templates/create_careers_widget.html:195
msgid "Download as HTML File"
msgstr "Télécharger en tant que fichier HTML"

#: templates/create_careers_widget.html:802
msgid ""
"Widget package downloaded! Extract and follow the README instructions for "
"integration."
msgstr "Package de widget téléchargé ! Extrayez et suivez les instructions README pour l'intégration."

#: templates/create_careers_widget.html:823
msgid "Widget file downloaded! Copy the code and paste it into your website."
msgstr "Fichier widget téléchargé ! Copiez le code et collez-le sur votre site."

#: templates/create_job.html:3
msgid "Create Job Position"
msgstr "Créer un poste"

#: templates/create_job.html:8
msgid "Basic Information"
msgstr "Informations de base"

#: templates/create_job.html:10 templates/job_details.html:1072
#: templates/job_preview_publish.html:590
msgid "Role Title"
msgstr "Intitulé du poste"

#: templates/create_job.html:14
msgid "e.g. Senior Software Engineer"
msgstr "ex. Ingénieur logiciel senior"

#: templates/create_job.html:18 templates/job_details.html:1076
#: templates/job_preview_publish.html:594
msgid "Office Location"
msgstr "Localisation du bureau"

#: templates/create_job.html:21
msgid "Select office location"
msgstr "Sélectionner la localisation du bureau"

#: templates/create_job.html:31
msgid "No office locations found. Please"
msgstr "Aucun lieu de bureau trouvé. Veuillez"

#: templates/create_job.html:32
msgid "add office locations"
msgstr "ajouter des lieux de bureau"

#: templates/create_job.html:32 templates/create_job.html:53
#: templates/create_job.html:74
msgid "in your preferences first."
msgstr "d'abord dans vos préférences."

#: templates/create_job.html:36
msgid "No locations available"
msgstr "Aucun lieu disponible"

#: templates/create_job.html:41 templates/job_details.html:1080
#: templates/job_preview_publish.html:598
msgid "Work Schedule"
msgstr "Horaire de travail"

#: templates/create_job.html:44 templates/create_job.html:65
msgid "Select an option"
msgstr "Sélectionner une option"

#: templates/create_job.html:52
msgid "No work schedules found. Please"
msgstr "Aucun horaire de travail trouvé. Veuillez"

#: templates/create_job.html:53
msgid "add work schedules"
msgstr "ajouter des horaires de travail"

#: templates/create_job.html:57
msgid "No work schedules available"
msgstr "Aucun horaire de travail disponible"

#: templates/create_job.html:62 templates/job_details.html:1084
#: templates/job_preview_publish.html:604
msgid "Office Schedule"
msgstr "Horaire de bureau"

#: templates/create_job.html:73
msgid "No office schedules found. Please"
msgstr "Aucun horaire de bureau trouvé. Veuillez"

#: templates/create_job.html:74
msgid "add office schedules"
msgstr "ajouter des horaires de bureau"

#: templates/create_job.html:78
msgid "No office schedules available"
msgstr "Aucun horaire de bureau disponible"

#: templates/create_job.html:86
msgid "Skills Requirements"
msgstr "Compétences requises"

#: templates/create_job.html:88
msgid "Skill"
msgstr "Compétence"

#: templates/create_job.html:93
msgid "e.g. JavaScript"
msgstr "ex. JavaScript"

#: templates/create_job.html:95 templates/create_job.html:169
msgid "Add"
msgstr "Ajouter"

#: templates/create_job.html:98
msgid "Choose Skills"
msgstr "Choisir des compétences"

#: templates/create_job.html:112
msgid "Selected Skills"
msgstr "Compétences sélectionnées"

#: templates/create_job.html:115
msgid "No skills selected yet"
msgstr "Aucune compétence sélectionnée"

#: templates/create_job.html:125
msgid "Salary Details (Optional)"
msgstr "Détails du salaire (Optionnel)"

#: templates/create_job.html:128
msgid "Minimum Salary"
msgstr "Salaire minimum"

#: templates/create_job.html:132
msgid "Enter minimum salary"
msgstr "Entrez le salaire minimum"

#: templates/create_job.html:136
msgid "Maximum Salary"
msgstr "Salaire maximum"

#: templates/create_job.html:140
msgid "Enter maximum salary"
msgstr "Entrez le salaire maximum"

#: templates/create_job.html:144
msgid "Currency"
msgstr "Devise"

#: templates/create_job.html:146
msgid "Select currency"
msgstr "Sélectionnez la devise"

#: templates/create_job.html:162
msgid "Benefits and Highlights (Optional)"
msgstr "Avantages et points forts (Optionnel)"

#: templates/create_job.html:167
msgid "e.g. Yearly Bonuses"
msgstr "ex. Bonus annuels"

#: templates/create_job.html:172
msgid "Choose Benefits"
msgstr "Choisir les avantages"

#: templates/create_job.html:175
msgid "Dental Coverage"
msgstr "Couverture dentaire"

#: templates/create_job.html:178
msgid "Private Health Coverage"
msgstr "Couverture santé privée"

#: templates/create_job.html:181
msgid "Gym membership"
msgstr "Adhésion à la salle de sport"

#: templates/create_job.html:184
msgid "Sign-in Bonus"
msgstr "Bonus de signature"

#: templates/create_job.html:187
msgid "Relocation Package"
msgstr "Forfait de déménagement"

#: templates/create_job.html:190
msgid "Company Vehicle"
msgstr "Véhicule de société"

#: templates/create_job.html:192
msgid "Food Card"
msgstr "Carte repas"

#: templates/create_job.html:194
msgid "Snacks & Coffee"
msgstr "Collations & Café"

#: templates/create_job.html:197
msgid "Pet Friendly Office"
msgstr "Bureau pet-friendly"

#: templates/create_job.html:201
msgid "Selected Benefits & Highlights"
msgstr "Avantages et points forts sélectionnés"

#: templates/create_job.html:204
msgid "No benefits or highlights selected yet"
msgstr "Aucun avantage ou point fort sélectionné"

#: templates/create_job.html:211 templates/job_details.html:107
#: templates/job_preview_publish.html:159
msgid "Discard"
msgstr "Abandonner"

#: templates/create_job.html:212
msgid "Next"
msgstr "Suivant"

#: templates/create_job_template.html:13 templates/create_job_template.html:19
#: templates/settings.html:41
msgid "Templates"
msgstr "Modèles"

#: templates/create_job_template.html:14
msgid "Create and manage reusable job description templates"
msgstr "Créez et gérez des modèles de description de poste réutilisables"

#: templates/create_job_template.html:17 templates/job_preferences.html:15
#: templates/manage_permissions.html:15 templates/navbar.html:104
#: templates/settings.html:9
msgid "Settings"
msgstr "Paramètres"

#: templates/create_job_template.html:31
msgid "Total Templates"
msgstr "Total des modèles"

#: templates/create_job_template.html:41
msgid "Created This Month"
msgstr "Créés ce mois-ci"

#: templates/create_job_template.html:51
msgid "Jobs Created from Templates"
msgstr "Emplois créés à partir de modèles"

#: templates/create_job_template.html:61
msgid "Time Saved Using Templates"
msgstr "Temps économisé avec les modèles"

#: templates/create_job_template.html:71
msgid "My Templates"
msgstr "Mes modèles"

#: templates/create_job_template.html:74 templates/create_job_template.html:105
#: templates/create_job_template.html:118
msgid "New Template"
msgstr "Nouveau modèle"

#: templates/create_job_template.html:80
msgid "Search templates..."
msgstr "Rechercher des modèles..."

#: templates/create_job_template.html:91
msgid "Updated"
msgstr "Mis à jour"

#: templates/create_job_template.html:94
msgid "Used 1 time"
msgstr "Utilisé 1 fois"

#: templates/create_job_template.html:96
msgid "Used"
msgstr "Utilisé"

#: templates/create_job_template.html:96
msgid "times"
msgstr "fois"

#: templates/create_job_template.html:107
msgid "Not saved yet"
msgstr "Pas encore enregistré"

#: templates/create_job_template.html:108
msgid "Not used yet"
msgstr "Pas encore utilisé"

#: templates/create_job_template.html:118
msgid "Enter template title"
msgstr "Entrez le titre du modèle"

#: templates/create_job_template.html:121
#: templates/create_job_template.html:196
msgid "Delete Template"
msgstr "Supprimer le modèle"

#: templates/create_job_template.html:126 templates/job_details.html:135
msgid "Save Template"
msgstr "Enregistrer le modèle"

#: templates/create_job_template.html:136
msgid "Heading 1"
msgstr "Titre 1"

#: templates/create_job_template.html:137
msgid "Heading 2"
msgstr "Titre 2"

#: templates/create_job_template.html:138
msgid "Heading 3"
msgstr "Titre 3"

#: templates/create_job_template.html:139
msgid "Paragraph"
msgstr "Paragraphe"

#: templates/create_job_template.html:143
msgid "Bold"
msgstr "Gras"

#: templates/create_job_template.html:144
msgid "Italic"
msgstr "Italique"

#: templates/create_job_template.html:145
msgid "Underline"
msgstr "Souligné"

#: templates/create_job_template.html:149
msgid "Bullet List"
msgstr "Liste à puces"

#: templates/create_job_template.html:150
msgid "Numbered List"
msgstr "Liste numérotée"

#: templates/create_job_template.html:179
msgid "Enter your template content here..."
msgstr "Entrez le contenu de votre modèle ici..."

#: templates/create_job_template.html:185 templates/job_details.html:100
msgid "characters"
msgstr "caractères"

#: templates/create_job_template.html:201
msgid "Are you sure you want to delete the"
msgstr "Êtes-vous sûr de vouloir supprimer le"

#: templates/create_job_template.html:201
msgid "template? This action cannot be undone."
msgstr "modèle ? Cette action est irréversible."

#: templates/create_job_template.html:206 templates/job_preferences.html:70
msgid "Delete"
msgstr "Supprimer"

#: templates/feed.html:20 templates/feed.html:46
msgid "Loading..."
msgstr "Chargement..."

#: templates/feed.html:32
msgid "Calendar"
msgstr "Calendrier"

#: templates/feed.html:34
msgid "Day"
msgstr "Jour"

#: templates/feed.html:35
msgid "Week"
msgstr "Semaine"

#: templates/feed.html:36
msgid "Month"
msgstr "Mois"

#: templates/feed.html:51 templates/jobs.html:92 templates/people.html:64
msgid "Today"
msgstr "Aujourd'hui"

#: templates/feed.html:56
msgid "Click on a day with colored dots to view events"
msgstr "Cliquez sur un jour avec des points colorés pour voir les événements"

#: templates/feed.html:61 templates/feed.html:74
msgid "Mon"
msgstr "Lun"

#: templates/feed.html:62 templates/feed.html:75
msgid "Tue"
msgstr "Mar"

#: templates/feed.html:63 templates/feed.html:76
msgid "Wed"
msgstr "Mer"

#: templates/feed.html:64 templates/feed.html:77
msgid "Thu"
msgstr "Jeu"

#: templates/feed.html:65 templates/feed.html:78
msgid "Fri"
msgstr "Ven"

#: templates/feed.html:66 templates/feed.html:79
msgid "Sat"
msgstr "Sam"

#: templates/feed.html:67 templates/feed.html:80
msgid "Sun"
msgstr "Dim"

#: templates/feed.html:96
msgid "Activity Feed"
msgstr "Flux d'activité"

#: templates/feed.html:100
msgid "Clear"
msgstr "Effacer"

#: templates/feed.html:147 templates/feed.html:3030
msgid "No Recent Activity"
msgstr "Aucune activité récente"

#: templates/feed.html:149 templates/feed.html:3032
msgid ""
"Activity will appear here when candidates apply, change status, or when you "
"post new jobs."
msgstr "L'activité apparaîtra ici lorsque les candidats postuleront, changeront de statut, ou lorsque vous publierez de nouveaux emplois."

#: templates/feed.html:166
msgid "Hot"
msgstr "Populaire"

#: templates/feed.html:166 templates/navbar.html:36
msgid "Jobs"
msgstr "Emplois"

#: templates/feed.html:169
msgid "View All Jobs"
msgstr "Voir tous les emplois"

#: templates/feed.html:186 templates/feed.html:245 templates/jobs.html:129
#: templates/navbar.html:42
msgid "Applicants"
msgstr "Candidats"

#: templates/feed.html:203
msgid "No Hot Jobs Yet"
msgstr "Aucun emploi populaire pour l'instant"

#: templates/feed.html:205
msgid ""
"Create your first job posting to start attracting candidates and see "
"trending positions here."
msgstr "Créez votre première offre d'emploi pour attirer des candidats et voir les postes tendance ici."

#: templates/feed.html:209 templates/navbar.html:57
msgid "Create Job"
msgstr "Créer un emploi"

#: templates/feed.html:212 templates/jobs.html:175
msgid "Browse Templates"
msgstr "Parcourir les modèles"

#: templates/feed.html:228
msgid "Monthly Applicant Overview"
msgstr "Aperçu mensuel des candidats"

#: templates/feed.html:267
msgid "Events for Date"
msgstr "Événements pour la date"

#: templates/feed.html:289
msgid "Add New Event"
msgstr "Ajouter un nouvel événement"

#: templates/feed.html:299
msgid "Create New Event"
msgstr "Créer un nouvel événement"

#: templates/feed.html:306
msgid "Enter event title"
msgstr "Entrez le titre de l'événement"

#: templates/feed.html:393
msgid "Select the relevant position"
msgstr "Sélectionnez le poste pertinent"

#: templates/feed.html:400
msgid "No vacancies available"
msgstr "Aucun poste vacant disponible"

#: templates/feed.html:413
msgid "Pick a Vacancy to see candidates"
msgstr "Choisissez un poste vacant pour voir les candidats"

#: templates/feed.html:436
msgid "Enter meeting link"
msgstr "Entrez le lien de la réunion"

#: templates/feed.html:2975
msgid ""
"Are you sure you want to clear all activity notifications? This action "
"cannot be undone."
msgstr "Êtes-vous sûr de vouloir effacer toutes les notifications d'activité ? Cette action est irréversible."

#: templates/feed.html:3046
msgid "Activity feed cleared successfully"
msgstr "Flux d'activité effacé avec succès"

#: templates/feed.html:3048
msgid "Failed to clear activity feed"
msgstr "Échec de l'effacement du flux d'activité"

#: templates/feed.html:3053
msgid "An error occurred while clearing activity feed"
msgstr "Une erreur s'est produite lors de l'effacement du flux d'activité"

#: templates/job_details.html:7 templates/job_details.html:20
#: templates/job_details.html:51 templates/job_preview_publish.html:28
msgid "Job Description"
msgstr "Description du poste"

#: templates/job_details.html:11 templates/job_preview_publish.html:17
msgid "Job Summary"
msgstr "Résumé du poste"

#: templates/job_details.html:14 templates/job_preview_publish.html:21
msgid "Loading job details..."
msgstr "Chargement des détails du poste..."

#: templates/job_details.html:24
msgid "Create new description"
msgstr "Créer une nouvelle description"

#: templates/job_details.html:28
msgid "Use saved template"
msgstr "Utiliser un modèle enregistré"

#: templates/job_details.html:33
msgid "Choose a template:"
msgstr "Choisissez un modèle :"

#: templates/job_details.html:35
msgid "Select a template"
msgstr "Sélectionner un modèle"

#: templates/job_details.html:42
msgid "AI Job Description Generator"
msgstr "Générateur de description de poste IA"

#: templates/job_details.html:43
msgid ""
"Let AI create a professional job description based on your job details above."
msgstr "Laissez l'IA créer une description de poste professionnelle basée sur vos détails ci-dessus."

#: templates/job_details.html:46
msgid "Generate with AI"
msgstr "Générer avec IA"

#: templates/job_details.html:52
msgid ""
"Describe the position, responsibilities, qualifications, and any other "
"relevant details."
msgstr "Décrivez le poste, les responsabilités, les qualifications et tout autre détail pertinent."

#: templates/job_details.html:106 templates/job_preview_publish.html:158
msgid "Back"
msgstr "Retour"

#: templates/job_details.html:111
msgid "Update Template"
msgstr "Mettre à jour le modèle"

#: templates/job_details.html:114 templates/job_details.html:124
msgid "Save as Template"
msgstr "Enregistrer comme modèle"

#: templates/job_details.html:117
msgid "Save & Continue"
msgstr "Enregistrer et continuer"

#: templates/job_details.html:129
msgid "Template Title"
msgstr "Titre du modèle"

#: templates/job_details.html:130
msgid "Enter a name for this template"
msgstr "Entrez un nom pour ce modèle"

#: templates/job_details.html:1060
msgid ""
"No job information found. Please go back and fill out the job details form."
msgstr "Aucune information sur le poste trouvée. Veuillez retourner et remplir le formulaire de détails du poste."

#: templates/job_details.html:1089 templates/job_preview_publish.html:610
msgid "Salary Details"
msgstr "Détails du salaire"

#: templates/job_details.html:1094 templates/job_preview_publish.html:617
msgid "Benefits & Highlights"
msgstr "Avantages & Points forts"

#: templates/job_details.html:1104 templates/job_preview_publish.html:633
msgid "Skills"
msgstr "Compétences"

#: templates/job_preferences.html:11 templates/job_preferences.html:17
#: templates/settings.html:20
msgid "Preferences"
msgstr "Préférences"

#: templates/job_preferences.html:12
msgid "Configure standard options to streamline your job creation process"
msgstr "Configurez des options standard pour rationaliser votre processus de création d'emploi"

#: templates/job_preferences.html:26 templates/job_preferences.html:51
msgid "Work Schedules"
msgstr "Horaires de travail"

#: templates/job_preferences.html:30 templates/job_preferences.html:84
msgid "Office Schedules"
msgstr "Horaires de bureau"

#: templates/job_preferences.html:34
msgid "Locations"
msgstr "Lieux"

#: templates/job_preferences.html:38
msgid "Departments"
msgstr "Départements"

#: templates/job_preferences.html:42
msgid "Language"
msgstr "Langue"

#: templates/job_preferences.html:52
msgid "Define standard work schedule types for your organization"
msgstr "Définissez des types d'horaires de travail standard pour votre organisation"

#: templates/job_preferences.html:56
msgid "Add Work Schedule"
msgstr "Ajouter un horaire de travail"

#: templates/job_preferences.html:64
msgid "Search work schedules..."
msgstr "Rechercher des horaires de travail..."

#: templates/job_preferences.html:67
msgid "Select All"
msgstr "Tout sélectionner"

#: templates/job_preferences.html:85
msgid "Define where and how employees work"
msgstr "Définissez où et comment les employés travaillent"

#: templates/job_preferences.html:89
msgid "Add Office Schedule"
msgstr "Ajouter un horaire de bureau"

#: templates/job_preferences.html:183
msgid "Language Settings"
msgstr "Paramètres de langue"

#: templates/job_preferences.html:184
msgid "Choose your preferred language for the application interface"
msgstr "Choisissez votre langue préférée pour l'interface de l'application"

#: templates/job_preferences.html:189
msgid "Interface Language"
msgstr "Langue de l'interface"

#: templates/job_preferences.html:190
msgid "Select the language you want to use for the application interface"
msgstr "Sélectionnez la langue que vous souhaitez utiliser pour l'interface de l'application"

#: templates/job_preferences.html:210
msgid "Current"
msgstr "Actuel"

#: templates/job_preferences.html:223 templates/published_job_details.html:652
#: templates/workloupe_platform.html:213
msgid "Note:"
msgstr "Note :"

#: templates/job_preferences.html:223
msgid ""
"Changing the language will refresh the page to apply the new language "
"settings."
msgstr "Changer la langue rafraîchira la page pour appliquer les nouveaux paramètres linguistiques."

#: templates/job_preview_publish.html:7 templates/job_preview_publish.html:162
msgid "Publish Job"
msgstr "Publier l'offre"

#: templates/job_preview_publish.html:11
msgid "Final Review"
msgstr "Revue finale"

#: templates/job_preview_publish.html:12
msgid "Please review the job details before publishing."
msgstr "Veuillez vérifier les détails du poste avant publication."

#: templates/job_preview_publish.html:32
msgid "Loading job description..."
msgstr "Chargement de la description du poste..."

#: templates/job_preview_publish.html:39
msgid "Publish To"
msgstr "Publier sur"

#: templates/job_preview_publish.html:41
msgid ""
"Select the job portals where you want to publish this job posting. <br> <br> "
"<i> if the portal you want to publish to is grayed out, it means that you "
"have not yet adjusted the related configuration settings. </i>"
msgstr "Sélectionnez les portails d'emploi où vous souhaitez publier cette offre. <br> <br> <i>Si le portail sur lequel vous souhaitez publier est grisé, cela signifie que vous n'avez pas encore ajusté les paramètres de configuration associés.</i>"

#: templates/job_preview_publish.html:61
msgid ""
"Professional networking platform with over 750 million users worldwide. "
"Selecting this option will open a new tab for you to complete the job "
"posting."
msgstr "Plateforme de réseautage professionnel avec plus de 750 millions d'utilisateurs dans le monde. Sélectionner cette option ouvrira un nouvel onglet pour compléter la publication d'emploi."

#: templates/job_preview_publish.html:82
msgid ""
"Job and company review site focusing on workplace transparency. Selecting "
"this option will open a new tab for you to complete the job posting."
msgstr "Site d'avis sur les emplois et les entreprises axé sur la transparence en milieu de travail. Sélectionner cette option ouvrira un nouvel onglet pour compléter la publication d'emploi."

#: templates/job_preview_publish.html:103
#, python-format
msgid ""
"Specialized job platform for tech and creative professionals powered by "
"Workloupe. Workloupe is 100%% free to use."
msgstr "Plateforme d'emploi spécialisée pour les professionnels tech et créatifs, propulsée par Workloupe. Workloupe est 100%% gratuit."

#: templates/job_preview_publish.html:125
msgid ""
"One of the biggest remote job focused job platforms in the world. Posting to "
"Himalayas is free. "
msgstr "L'une des plus grandes plateformes d'emploi à distance au monde. Publier sur Himalayas est gratuit."

#: templates/job_preview_publish.html:147
msgid ""
"PostJobFree has more than 7 million jobs, and it's free to post to. Their "
"job portal is focused on simplicity and ease of use."
msgstr "PostJobFree compte plus de 7 millions d'offres d'emploi et la publication est gratuite. Leur portail d'emploi est axé sur la simplicité et la facilité d'utilisation."

#: templates/job_preview_publish.html:580
msgid ""
"No job information found. Please go back and fill out the job details form. "
msgstr "Aucune information sur le poste trouvée. Veuillez retourner et remplir le formulaire de détails du poste."

#: templates/job_preview_publish.html:658
msgid "No job description found. Please go back and create a job description."
msgstr "Aucune description de poste trouvée. Veuillez retourner et créer une description de poste."

#: templates/jobs.html:8
msgid "Job Listings"
msgstr "Listes d'emplois"

#: templates/jobs.html:18
msgid "Active Jobs"
msgstr "Emplois actifs"

#: templates/jobs.html:28
msgid "Total Applicants"
msgstr "Total des candidats"

#: templates/jobs.html:38
msgid "Archived Jobs"
msgstr "Emplois archivés"

#: templates/jobs.html:48
msgid "On-Hold Jobs"
msgstr "Emplois en attente"

#: templates/jobs.html:59 templates/profile.html:119
msgid "Department"
msgstr "Département"

#: templates/jobs.html:61
msgid "All Departments"
msgstr "Tous les départements"

#: templates/jobs.html:71 templates/manage_permissions.html:212
#: templates/people.html:43
msgid "All Statuses"
msgstr "Tous les statuts"

#: templates/jobs.html:79 templates/people.html:51 templates/people.html:90
msgid "Location"
msgstr "Lieu"

#: templates/jobs.html:81 templates/people.html:53
msgid "All Locations"
msgstr "Tous les lieux"

#: templates/jobs.html:89
msgid "Posted Date"
msgstr "Date de publication"

#: templates/jobs.html:91
msgid "All Time"
msgstr "Toutes périodes"

#: templates/jobs.html:93 templates/people.html:65
msgid "This Week"
msgstr "Cette semaine"

#: templates/jobs.html:94 templates/people.html:66
msgid "This Month"
msgstr "Ce mois-ci"

#: templates/jobs.html:95
msgid "Last Month"
msgstr "Le mois dernier"

#: templates/jobs.html:106 templates/people.html:76
msgid "Clear all filters"
msgstr "Effacer tous les filtres"

#: templates/jobs.html:135
msgid "Interviews"
msgstr "Entretiens"

#: templates/jobs.html:146
msgid "Days Open"
msgstr "Jours ouverts"

#: templates/jobs.html:152
msgid "Closed on:"
msgstr "Fermé le :"

#: templates/jobs.html:152
msgid "Posted on:"
msgstr "Publié le :"

#: templates/jobs.html:153
msgid "View Details"
msgstr "Voir les détails"

#: templates/jobs.html:166
msgid "No Job Postings Yet"
msgstr "Aucune offre d'emploi pour l'instant"

#: templates/jobs.html:168
msgid ""
"You haven't published any job postings yet. Create your first job posting to "
"start attracting candidates."
msgstr "Vous n'avez pas encore publié d'offres d'emploi. Créez votre première offre pour commencer à attirer des candidats."

#: templates/jobs.html:172
msgid "Create Your First Job"
msgstr "Créez votre premier emploi"

#: templates/jobs.html:186 templates/people.html:174
msgid "Showing"
msgstr "Affichage"

#: templates/jobs.html:186 templates/people.html:174
msgid "of"
msgstr "sur"

#: templates/manage_permissions.html:11
msgid "Team & Invitations"
msgstr "Équipe & Invitations"

#: templates/manage_permissions.html:12
msgid "Manage your recruitment team and invite new members"
msgstr "Gérez votre équipe de recrutement et invitez de nouveaux membres"

#: templates/manage_permissions.html:17 templates/manage_permissions.html:39
#: templates/manage_permissions.html:73 templates/settings.html:62
msgid "Invitations"
msgstr "Invitations"

#: templates/manage_permissions.html:29 templates/manage_permissions.html:69
msgid "Team Members"
msgstr "Membres de l'équipe"

#: templates/manage_permissions.html:59
msgid "Administrators"
msgstr "Administrateurs"

#: templates/manage_permissions.html:84
msgid "Search team members..."
msgstr "Rechercher des membres d'équipe..."

#: templates/manage_permissions.html:88 templates/manage_permissions.html:218
msgid "Role:"
msgstr "Rôle :"

#: templates/manage_permissions.html:90 templates/manage_permissions.html:220
msgid "All Roles"
msgstr "Tous les rôles"

#: templates/manage_permissions.html:105 templates/manage_permissions.html:244
#: templates/people.html:87 templates/profile.html:49
#: templates/published_job_details.html:276
msgid "Name"
msgstr "Nom"

#: templates/manage_permissions.html:109 templates/manage_permissions.html:248
#: templates/profile.html:57 templates/profile.html:101
#: templates/register.html:24
msgid "Email"
msgstr "E-mail"

#: templates/manage_permissions.html:113 templates/manage_permissions.html:252
#: templates/manage_permissions.html:381
msgid "Role"
msgstr "Rôle"

#: templates/manage_permissions.html:120 templates/manage_permissions.html:267
#: templates/profile.html:80 templates/published_job_details.html:285
msgid "Actions"
msgstr "Actions"

#: templates/manage_permissions.html:143
msgid "Deactivate"
msgstr "Désactiver"

#: templates/manage_permissions.html:147
msgid "Activate"
msgstr "Activer"

#: templates/manage_permissions.html:206
msgid "Search invitations..."
msgstr "Rechercher des invitations..."

#: templates/manage_permissions.html:210
msgid "Status:"
msgstr "Statut :"

#: templates/manage_permissions.html:231
msgid "Invite New Member"
msgstr "Inviter un nouveau membre"

#: templates/manage_permissions.html:256
msgid "Sent Date"
msgstr "Date d'envoi"

#: templates/manage_permissions.html:260
msgid "Expiry Date"
msgstr "Date d'expiration"

#: templates/manage_permissions.html:305
msgid "No invitations found"
msgstr "Aucune invitation trouvée"

#: templates/manage_permissions.html:353
msgid "Invite New Team Member"
msgstr "Inviter un nouveau membre d'équipe"

#: templates/manage_permissions.html:360
msgid "Recipient Information"
msgstr "Informations du destinataire"

#: templates/manage_permissions.html:364 templates/profile.html:93
msgid "First Name"
msgstr "Prénom"

#: templates/manage_permissions.html:365
msgid "Enter first name"
msgstr "Entrez le prénom"

#: templates/manage_permissions.html:369 templates/profile.html:97
msgid "Last Name"
msgstr "Nom de famille"

#: templates/manage_permissions.html:370
msgid "Enter last name"
msgstr "Entrez le nom de famille"

#: templates/manage_permissions.html:375 templates/signin.html:17
msgid "Email Address"
msgstr "Adresse e-mail"

#: templates/manage_permissions.html:376
msgid "Enter email address"
msgstr "Entrez l'adresse e-mail"

#: templates/manage_permissions.html:383 templates/manage_permissions.html:443
msgid "Select a role"
msgstr "Sélectionnez un rôle"

#: templates/manage_permissions.html:384 templates/manage_permissions.html:444
msgid "Administrator"
msgstr "Administrateur"

#: templates/manage_permissions.html:385 templates/manage_permissions.html:437
#: templates/manage_permissions.html:445
msgid "Recruiter"
msgstr "Recruteur"

#: templates/manage_permissions.html:386 templates/manage_permissions.html:446
msgid "Hiring Manager"
msgstr "Responsable d'embauche"

#: templates/manage_permissions.html:387 templates/manage_permissions.html:447
msgid "Interviewer"
msgstr "Intervieweur"

#: templates/manage_permissions.html:388 templates/manage_permissions.html:448
msgid "Read Only"
msgstr "Lecture seule"

#: templates/manage_permissions.html:395
msgid "Permissions"
msgstr "Permissions"

#: templates/manage_permissions.html:396
msgid ""
"Permissions are determined by the selected role. You can customize them "
"after the user has accepted the invitation."
msgstr "Les permissions sont déterminées par le rôle sélectionné. Vous pouvez les personnaliser après que l'utilisateur a accepté l'invitation."

#: templates/manage_permissions.html:400
msgid "Role Descriptions:"
msgstr "Descriptions des rôles :"

#: templates/manage_permissions.html:402
msgid "Administrator: Full access to all system features and settings."
msgstr "Administrateur : Accès complet à toutes les fonctionnalités et paramètres du système."

#: templates/manage_permissions.html:403
msgid "Recruiter: Manage job postings, candidates, and interviews."
msgstr "Recruteur : Gérer les offres d'emploi, les candidats et les entretiens."

#: templates/manage_permissions.html:404
msgid "Hiring Manager: Review candidates and make hiring decisions."
msgstr "Responsable d'embauche : Examiner les candidats et prendre des décisions d'embauche."

#: templates/manage_permissions.html:405
msgid "Interviewer: Conduct interviews and provide feedback."
msgstr "Intervieweur : Mener des entretiens et fournir des retours."

#: templates/manage_permissions.html:406
msgid "Read Only: View-only access to recruitment data."
msgstr "Lecture seule : Accès en lecture seule aux données de recrutement."

#: templates/manage_permissions.html:414
msgid "Send Invitation"
msgstr "Envoyer l'invitation"

#: templates/manage_permissions.html:425 templates/manage_permissions.html:464
msgid "Change Role"
msgstr "Changer de rôle"

#: templates/manage_permissions.html:431
msgid "Team Member"
msgstr "Membre d'équipe"

#: templates/manage_permissions.html:436
msgid "Current Role"
msgstr "Rôle actuel"

#: templates/manage_permissions.html:441
msgid "New Role*"
msgstr "Nouveau rôle*"

#: templates/manage_permissions.html:453
msgid "Reason for Change (Optional)"
msgstr "Raison du changement (Optionnel)"

#: templates/manage_permissions.html:454
msgid "Provide a reason for this role change"
msgstr "Fournissez une raison pour ce changement de rôle"

#: templates/manage_permissions.html:459
msgid ""
"Changing roles will update the user's permissions. They will be notified of "
"this change."
msgstr "Changer de rôle mettra à jour les permissions de l'utilisateur. Il en sera notifié."

#: templates/navbar.html:31
msgid "Feed"
msgstr "Flux"

#: templates/navbar.html:94
msgid "Employee"
msgstr "Employé"

#: templates/navbar.html:100
msgid "Profile"
msgstr "Profil"

#: templates/navbar.html:109
msgid "Logout"
msgstr "Déconnexion"

#: templates/navbar.html:118
msgid "Guest User"
msgstr "Utilisateur invité"

#: templates/navbar.html:121
msgid "Not logged in"
msgstr "Non connecté"

#: templates/navbar.html:126 templates/signin.html:97
msgid "Sign In"
msgstr "Connexion"

#: templates/people.html:10
msgid "Applicant Tracking"
msgstr "Suivi des candidats"

#: templates/people.html:14 templates/people.html:1034
msgid "Refresh Applicants"
msgstr "Actualiser les candidats"

#: templates/people.html:18
msgid "Search applicants..."
msgstr "Rechercher des candidats..."

#: templates/people.html:33
msgid "All Positions"
msgstr "Tous les postes"

#: templates/people.html:63
msgid "All Dates"
msgstr "Toutes dates"

#: templates/people.html:91
msgid "Experience (Years)"
msgstr "Expérience (années)"

#: templates/people.html:92 templates/published_job_details.html:282
msgid "Score"
msgstr "Score"

#: templates/people.html:93
msgid "Applied On"
msgstr "Postulé le"

#: templates/people.html:94
msgid "Action"
msgstr "Action"

#: templates/people.html:139 templates/published_job_details.html:343
msgid "View"
msgstr "Voir"

#: templates/people.html:154 templates/published_job_details.html:246
msgid "No Applicants Yet"
msgstr "Aucun candidat pour l'instant"

#: templates/people.html:156
msgid ""
"Nobody has applied to your job postings yet. Once candidates start applying, "
"you'll see them here."
msgstr "Personne n'a encore postulé à vos offres d'emploi. Une fois que les candidats commenceront à postuler, vous les verrez ici."

#: templates/people.html:160
msgid "View Job Postings"
msgstr "Voir les offres d'emploi"

#: templates/people.html:163
msgid "Create New Job"
msgstr "Créer un nouvel emploi"

#: templates/people.html:174
msgid "applicants"
msgstr "candidats"

#: templates/people.html:227
msgid "Show"
msgstr "Afficher"

#: templates/people.html:234
msgid "per page"
msgstr "par page"

#: templates/people.html:969
msgid "Processing..."
msgstr "Traitement..."

#: templates/people.html:1015
msgid "Success!"
msgstr "Succès !"

#: templates/people.html:1022 templates/workloupe_platform.html:1119
msgid "Error:"
msgstr "Erreur :"

#: templates/people.html:1027
msgid "An error occurred while processing applications. Please try again."
msgstr "Une erreur s'est produite lors du traitement des candidatures. Veuillez réessayer."

#: templates/profile.html:10
msgid "Your Profile"
msgstr "Votre profil"

#: templates/profile.html:29
msgid "Change Photo"
msgstr "Changer la photo"

#: templates/profile.html:32
msgid "Profile Activity"
msgstr "Activité du profil"

#: templates/profile.html:33
msgid "Last Login:"
msgstr "Dernière connexion :"

#: templates/profile.html:36
msgid "Account Created:"
msgstr "Compte créé le :"

#: templates/profile.html:46 templates/profile.html:91
msgid "Personal Information"
msgstr "Informations personnelles"

#: templates/profile.html:63 templates/profile.html:109
#: templates/workloupe_platform.html:28
msgid "Company Information"
msgstr "Informations sur l'entreprise"

#: templates/profile.html:66 templates/profile.html:111
msgid "Company"
msgstr "Entreprise"

#: templates/profile.html:83 templates/profile.html:163
#: templates/profile.html:185
msgid "Change Password"
msgstr "Changer le mot de passe"

#: templates/profile.html:105
msgid "Phone"
msgstr "Téléphone"

#: templates/profile.html:124
msgid "Save Changes"
msgstr "Enregistrer les modifications"

#: templates/profile.html:139
msgid "Upload Profile Photo"
msgstr "Télécharger une photo de profil"

#: templates/profile.html:146
msgid "Choose a photo (PNG or JPEG only)"
msgstr "Choisissez une photo (PNG ou JPEG uniquement)"

#: templates/profile.html:153
msgid "Upload"
msgstr "Télécharger"

#: templates/profile.html:170
msgid "Current Password"
msgstr "Mot de passe actuel"

#: templates/profile.html:174
msgid "New Password"
msgstr "Nouveau mot de passe"

#: templates/profile.html:178
msgid "Confirm New Password"
msgstr "Confirmer le nouveau mot de passe"

#: templates/published_job_details.html:58
msgid "Notification"
msgstr "Notification"

#: templates/published_job_details.html:82
msgid "Total Applicants:"
msgstr "Total des candidats :"

#: templates/published_job_details.html:87
msgid "Published At:"
msgstr "Publié le :"

#: templates/published_job_details.html:111
msgid "Bulk Communication"
msgstr "Communication groupée"

#: templates/published_job_details.html:124
msgid "Expert Support Options"
msgstr "Options de support expert"

#: templates/published_job_details.html:136
msgid "Post on LinkedIn"
msgstr "Publier sur LinkedIn"

#: templates/published_job_details.html:148
#: templates/published_job_details.html:641
msgid "Change Vacancy Status"
msgstr "Changer le statut du poste"

#: templates/published_job_details.html:162
msgid "Applicants Over Time"
msgstr "Candidats au fil du temps"

#: templates/published_job_details.html:170
msgid "No Application Data Yet"
msgstr "Aucune donnée de candidature pour l'instant"

#: templates/published_job_details.html:171
msgid ""
"Once candidates start applying, you'll see application trends over time here."
msgstr "Une fois que les candidats commenceront à postuler, vous verrez les tendances des candidatures ici."

#: templates/published_job_details.html:184
msgid "Number of Applicants by Job Portal"
msgstr "Nombre de candidats par portail d'emploi"

#: templates/published_job_details.html:191
msgid "No Portal Data Yet"
msgstr "Aucune donnée de portail pour l'instant"

#: templates/published_job_details.html:192
msgid ""
"When applications come in, you'll see which job portals are most effective "
"here."
msgstr "Lorsque les candidatures arriveront, vous verrez quels portails d'emploi sont les plus efficaces ici."

#: templates/published_job_details.html:202
msgid "Distribution of Applicants by Status"
msgstr "Répartition des candidats par statut"

#: templates/published_job_details.html:209
msgid "No Status Data Yet"
msgstr "Aucune donnée de statut pour l'instant"

#: templates/published_job_details.html:210
msgid ""
"Application status distribution will appear here as you review candidates."
msgstr "La répartition des statuts de candidature apparaîtra ici pendant que vous examinez les candidats."

#: templates/published_job_details.html:225
msgid "Top Applicants"
msgstr "Meilleurs candidats"

#: templates/published_job_details.html:233
msgid "View All Applicants"
msgstr "Voir tous les candidats"

#: templates/published_job_details.html:249
msgid ""
"Don't worry! Once candidates start applying to this position, they will "
"appear here. You can track their progress, review their profiles, and manage "
"the hiring process."
msgstr "Ne vous inquiétez pas ! Une fois que les candidats commenceront à postuler à ce poste, ils apparaîtront ici. Vous pouvez suivre leur progression, examiner leurs profils et gérer le processus d'embauche."

#: templates/published_job_details.html:256
msgid "What happens next?"
msgstr "Que se passe-t-il ensuite ?"

#: templates/published_job_details.html:259
msgid "Candidates will apply through your job posting"
msgstr "Les candidats postuleront via votre offre d'emploi"

#: templates/published_job_details.html:260
msgid "You'll see their profiles and CVs here"
msgstr "Vous verrez leurs profils et CV ici"

#: templates/published_job_details.html:261
msgid "You can review, rate, and manage applications"
msgstr "Vous pouvez examiner, évaluer et gérer les candidatures"

#: templates/published_job_details.html:262
msgid "Use the communication tools to contact candidates"
msgstr "Utilisez les outils de communication pour contacter les candidats"

#: templates/published_job_details.html:332
msgid "Not Rated"
msgstr "Non évalué"

#: templates/published_job_details.html:360
msgid "Request Support From Experts"
msgstr "Demander un support d'experts"

#: templates/published_job_details.html:365
msgid ""
"We can provide vetted candidates from our talent pool, or help you during "
"the technical interviews to pick best fit for your expectations."
msgstr "Nous pouvons fournir des candidats vérifiés issus de notre vivier de talents, ou vous aider pendant les entretiens techniques à choisir le meilleur profil pour vos attentes."

#: templates/published_job_details.html:370
msgid "Enter Details"
msgstr "Entrer les détails"

#: templates/published_job_details.html:381
msgid "Request Candidates"
msgstr "Demander des candidats"

#: templates/published_job_details.html:382
msgid "Request Interview Help"
msgstr "Demander de l'aide pour l'entretien"

#: templates/published_job_details.html:396
msgid "Send Bulk Mail to Applicants"
msgstr "Envoyer un e-mail groupé aux candidats"

#: templates/published_job_details.html:401
msgid ""
"Use this form to send bulk emails to applicants based on their application "
"statuses."
msgstr "Utilisez ce formulaire pour envoyer des e-mails groupés aux candidats en fonction de leurs statuts de candidature."

#: templates/published_job_details.html:406
msgid "Select Application Status"
msgstr "Sélectionner le statut de candidature"

#: templates/published_job_details.html:413
msgid "Select a status"
msgstr "Sélectionner un statut"

#: templates/published_job_details.html:420
msgid "Email Subject"
msgstr "Sujet de l'e-mail"

#: templates/published_job_details.html:439
msgid "Enter internal notes for your team (optional)"
msgstr "Entrez des notes internes pour votre équipe (optionnel)"

#: templates/published_job_details.html:443
msgid "(sent to candidates)"
msgstr "(envoyé aux candidats)"

#: templates/published_job_details.html:450
msgid "Enter your email message"
msgstr "Entrez votre message e-mail"

#: templates/published_job_details.html:462
msgid "Send notification emails to candidates"
msgstr "Envoyer des e-mails de notification aux candidats"

#: templates/published_job_details.html:466
msgid "Send Emails"
msgstr "Envoyer les e-mails"

#: templates/published_job_details.html:482
msgid "Post this job on LinkedIn"
msgstr "Publier cet emploi sur LinkedIn"

#: templates/published_job_details.html:489
msgid ""
"Follow these simple steps to post your job on LinkedIn and reach more "
"candidates."
msgstr "Suivez ces étapes simples pour publier votre offre sur LinkedIn et atteindre plus de candidats."

#: templates/published_job_details.html:496
msgid "Navigate to LinkedIn Job Posting"
msgstr "Accédez à la publication d'emploi LinkedIn"

#: templates/published_job_details.html:499
msgid "Go to"
msgstr "Aller à"

#: templates/published_job_details.html:499
msgid "LinkedIn Job Posting Page"
msgstr "Page de publication d'emploi LinkedIn"

#: templates/published_job_details.html:507
msgid "Copy Job Title"
msgstr "Copier le titre du poste"

#: templates/published_job_details.html:509
msgid "Copy and paste the job title below:"
msgstr "Copiez et collez le titre du poste ci-dessous :"

#: templates/published_job_details.html:526
msgid "Configure Job Settings"
msgstr "Configurer les paramètres du poste"

#: templates/published_job_details.html:529
msgid "Select 'Use my own description' option and configure job details:"
msgstr "Sélectionnez l'option 'Utiliser ma propre description' et configurez les détails du poste :"

#: templates/published_job_details.html:531
msgid "Reference - Your job settings:"
msgstr "Référence - Vos paramètres de poste :"

#: templates/published_job_details.html:533
msgid "Location:"
msgstr "Lieu :"

#: templates/published_job_details.html:534
msgid "Work Schedule:"
msgstr "Horaire de travail :"

#: templates/published_job_details.html:535
msgid "Office Schedule:"
msgstr "Horaire de bureau :"

#: templates/published_job_details.html:545
msgid "Copy Job Description"
msgstr "Copier la description du poste"

#: templates/published_job_details.html:548
msgid "Copy and paste the job description below:"
msgstr "Copiez et collez la description du poste ci-dessous :"

#: templates/published_job_details.html:551
msgid ""
"Note: Delete any placeholder text in LinkedIn's editor before pasting. You "
"may have to re-apply some of the styling."
msgstr "Note : Supprimez tout texte d'espace réservé dans l'éditeur LinkedIn avant de coller. Vous devrez peut-être réappliquer une partie de la mise en forme."

#: templates/published_job_details.html:570
msgid "Configure Application Management"
msgstr "Configurer la gestion des candidatures"

#: templates/published_job_details.html:574
msgid "Click 'Continue' button"
msgstr "Cliquez sur le bouton 'Continuer'"

#: templates/published_job_details.html:575
msgid "Find 'Manage applicants' option and click the pencil icon to edit"
msgstr "Trouvez l'option 'Gérer les candidats' et cliquez sur l'icône crayon pour modifier"

#: templates/published_job_details.html:576
msgid ""
"Change 'Manage Applications' from 'On LinkedIn' to 'On an External Website'"
msgstr "Changez 'Gérer les candidatures' de 'Sur LinkedIn' à 'Sur un site web externe'"

#: templates/published_job_details.html:577
msgid "Copy and paste the application URL below:"
msgstr "Copiez et collez l'URL de candidature ci-dessous :"

#: templates/published_job_details.html:596
msgid "Review Qualifications"
msgstr "Examiner les qualifications"

#: templates/published_job_details.html:599
msgid "Review and customize the ideal qualifications section:"
msgstr "Examinez et personnalisez la section des qualifications idéales :"

#: templates/published_job_details.html:601
msgid "Reference - Skills from your job:"
msgstr "Référence - Compétences de votre offre :"

#: templates/published_job_details.html:615
msgid "Finalize and Publish"
msgstr "Finaliser et publier"

#: templates/published_job_details.html:619
msgid "Confirm your identity using your work email if required"
msgstr "Confirmez votre identité en utilisant votre e-mail professionnel si nécessaire"

#: templates/published_job_details.html:620
msgid "Choose between free or promoted posting (Recommended: Free)"
msgstr "Choisissez entre une publication gratuite ou promue (Recommandé : Gratuit)"

#: templates/published_job_details.html:621
msgid "Click 'Post Job' button"
msgstr "Cliquez sur le bouton 'Publier l'offre'"

#: templates/published_job_details.html:622
msgid "Your job is now live on LinkedIn!"
msgstr "Votre offre est maintenant en ligne sur LinkedIn !"

#: templates/published_job_details.html:629
msgid ""
"Need help? Contact our support team if you encounter any issues during the "
"posting process."
msgstr "Besoin d'aide ? Contactez notre équipe de support si vous rencontrez des problèmes pendant le processus de publication."

#: templates/published_job_details.html:646
msgid "Current Status:"
msgstr "Statut actuel :"

#: templates/published_job_details.html:652
msgid "Changing the status will affect the visibility of the vacancy."
msgstr "Changer le statut affectera la visibilité du poste vacant."

#: templates/published_job_details.html:655
msgid ""
"The vacancy will no longer exist on boards and be closed but be accesible "
"internally."
msgstr "Le poste vacant ne sera plus disponible sur les tableaux et sera fermé mais restera accessible en interne."

#: templates/published_job_details.html:656
msgid "The vacancy will stop accepting new applications until changed."
msgstr "Le poste vacant cessera d'accepter de nouvelles candidatures jusqu'à modification."

#: templates/published_job_details.html:657
msgid "The vacancy will be re-opened for new applications."
msgstr "Le poste vacant sera rouvert aux nouvelles candidatures."

#: templates/published_job_details.html:658
msgid "The vacancy will be permanently deleted. This action cannot be undone."
msgstr "Le poste vacant sera définitivement supprimé. Cette action est irréversible."

#: templates/published_job_details.html:664
msgid "Select New Status"
msgstr "Sélectionner un nouveau statut"

#: templates/published_job_details.html:687
msgid "Confirm Status"
msgstr "Confirmer le statut"

#: templates/register.html:11
msgid "Accept Invitation"
msgstr "Accepter l'invitation"

#: templates/register.html:15
msgid "This invitation has expired or already been used."
msgstr "Cette invitation a expiré ou a déjà été utilisée."

#: templates/register.html:18
msgid "Hello"
msgstr "Bonjour"

#: templates/register.html:18
msgid "you've been invited to join"
msgstr "vous êtes invité à rejoindre"

#: templates/register.html:18
msgid "as a"
msgstr "en tant que"

#: templates/register.html:29
msgid "Create Password"
msgstr "Créer un mot de passe"

#: templates/register.html:34
msgid "Confirm Password"
msgstr "Confirmer le mot de passe"

#: templates/register.html:39
msgid "Complete Registration"
msgstr "Terminer l'inscription"

#: templates/register.html:58
msgid "Passwords do not match"
msgstr "Les mots de passe ne correspondent pas"

#: templates/registration_complete.html:14
msgid "Registration Complete!"
msgstr "Inscription terminée !"

#: templates/registration_complete.html:15
msgid ""
"Your account has been created successfully. You can now log in to access the "
"system."
msgstr "Votre compte a été créé avec succès. Vous pouvez maintenant vous connecter pour accéder au système."

#: templates/registration_complete.html:17
msgid "Go to Login"
msgstr "Aller à la connexion"

#: templates/settings.html:10
msgid "Configure your recruitment workflow and manage your ATS settings"
msgstr "Configurez votre flux de recrutement et gérez les paramètres de votre ATS"

#: templates/settings.html:21
msgid ""
"Configure default options for job creation including work schedules, office "
"locations, and role titles."
msgstr "Configurez les options par défaut pour la création d'emploi, y compris les horaires de travail, les emplacements de bureau et les intitulés de poste."

#: templates/settings.html:23
msgid "Define company work schedules"
msgstr "Définir les horaires de travail de l'entreprise"

#: templates/settings.html:24
msgid "Set up office locations"
msgstr "Configurer les emplacements de bureau"

#: templates/settings.html:25
msgid "Standardize role titles"
msgstr "Standardiser les intitulés de poste"

#: templates/settings.html:26
msgid "Configure office schedule options"
msgstr "Configurer les options d'horaire de bureau"

#: templates/settings.html:29
msgid "Manage Preferences"
msgstr "Gérer les préférences"

#: templates/settings.html:42
msgid ""
"Create, edit, and manage job description templates to streamline your job "
"posting process."
msgstr "Créez, modifiez et gérez des modèles de description de poste pour rationaliser votre processus de publication d'offres."

#: templates/settings.html:44
msgid "Build reusable job templates"
msgstr "Construire des modèles d'emploi réutilisables"

#: templates/settings.html:45
msgid "Save time on repetitive descriptions"
msgstr "Gagner du temps sur les descriptions répétitives"

#: templates/settings.html:46
msgid "Maintain consistent job postings"
msgstr "Maintenir des offres d'emploi cohérentes"

#: templates/settings.html:47
msgid "Organize templates by department"
msgstr "Organiser les modèles par département"

#: templates/settings.html:50
msgid "Manage Templates"
msgstr "Gérer les modèles"

#: templates/settings.html:63
msgid ""
"Invite team members to collaborate on your recruitment process and manage "
"user access."
msgstr "Invitez des membres d'équipe à collaborer sur votre processus de recrutement et gérez l'accès des utilisateurs."

#: templates/settings.html:65
msgid "Add colleagues to your ATS"
msgstr "Ajouter des collègues à votre ATS"

#: templates/settings.html:66
msgid "Set user permissions"
msgstr "Définir les permissions utilisateur"

#: templates/settings.html:67
msgid "Track invitation status"
msgstr "Suivre l'état des invitations"

#: templates/settings.html:68
msgid "Manage team collaboration"
msgstr "Gérer la collaboration d'équipe"

#: templates/settings.html:71
msgid "Manage Invitations"
msgstr "Gérer les invitations"

#: templates/settings.html:84
msgid "Job Portals"
msgstr "Portails d'emploi"

#: templates/settings.html:85
msgid ""
"Configure connections to external job boards and manage API credentials for "
"job publishing."
msgstr "Configurez les connexions aux plateformes d'emploi externes et gérez les identifiants API pour la publication d'offres."

#: templates/settings.html:87
msgid "Connect to major job boards"
msgstr "Se connecter aux principales plateformes d'emploi"

#: templates/settings.html:88
msgid "Manage API tokens securely"
msgstr "Gérer les jetons API en toute sécurité"

#: templates/settings.html:89
msgid "Customize portal preferences"
msgstr "Personnaliser les préférences des portails"

#: templates/settings.html:90
msgid "Track portal integration status"
msgstr "Suivre l'état de l'intégration des portails"

#: templates/settings.html:93
msgid "(Coming Soon!)"
msgstr "(Bientôt disponible !)"

#: templates/settings.html:106
msgid "Careers Page & Workloupe Configurations"
msgstr "Page Carrières & Configurations Workloupe"

#: templates/settings.html:107
msgid ""
"Adjust the look and feel of your company careers page and workloupe profile "
"to match your branding."
msgstr "Ajustez l'apparence de votre page carrières d'entreprise et de votre profil Workloupe pour correspondre à votre image de marque."

#: templates/settings.html:109
msgid "Upload company photos"
msgstr "Télécharger des photos de l'entreprise"

#: templates/settings.html:110
msgid "Pick your colours"
msgstr "Choisissez vos couleurs"

#: templates/settings.html:111
msgid "Choose the components"
msgstr "Choisissez les composants"

#: templates/settings.html:112
msgid "Upload your company logo and banner"
msgstr "Téléchargez votre logo et bannière d'entreprise"

#: templates/settings.html:115
msgid "Manage Carees Page & Workloupe Configurations"
msgstr "Gérer la page Carrières & Configurations Workloupe"

#: templates/settings.html:126
msgid "Need Help?"
msgstr "Besoin d'aide ?"

#: templates/settings.html:127
msgid ""
"Our support team is ready to assist you with any questions about configuring "
"your ATS."
msgstr "Notre équipe de support est prête à vous aider pour toute question concernant la configuration de votre ATS."

#: templates/settings.html:128
msgid "Contact Support"
msgstr "Contacter le support"

#: templates/signin.html:9
msgid "Welcome Back"
msgstr "Content de vous revoir"

#: templates/signin.html:10
msgid "Sign in to your account"
msgstr "Connectez-vous à votre compte"

#: templates/signin.html:24
msgid "Enter your email"
msgstr "Entrez votre e-mail"

#: templates/signin.html:38
msgid "Password"
msgstr "Mot de passe"

#: templates/signin.html:45
msgid "Enter your password"
msgstr "Entrez votre mot de passe"

#: templates/signin.html:72
msgid "Security Check"
msgstr "Vérification de sécurité"

#: templates/signin.html:72
msgid "What is"
msgstr "Combien font"

#: templates/signin.html:79
msgid "Enter the answer"
msgstr "Entrez la réponse"

#: templates/signin.html:89
msgid "Remember me"
msgstr "Se souvenir de moi"

#: templates/signin.html:92
msgid "Forgot password?"
msgstr "Mot de passe oublié ?"

#: templates/signin.html:106
msgid "Don't have an account?"
msgstr "Vous n'avez pas de compte ?"

#: templates/signin.html:108
msgid "Contact us"
msgstr "Contactez-nous"

#: templates/wordpress_integration.html:14
msgid "WordPress Integration"
msgstr "Intégration WordPress"

#: templates/wordpress_integration.html:15
msgid "Configure your WordPress careers page"
msgstr "Configurez votre page carrières WordPress"

#: templates/wordpress_integration.html:23
msgid "WordPress Setup"
msgstr "Configuration WordPress"

#: templates/wordpress_integration.html:28
msgid "Choose your preferred WordPress integration method"
msgstr "Choisissez votre méthode d'intégration WordPress préférée"

#: templates/wordpress_integration.html:32
msgid "Integration Method"
msgstr "Méthode d'intégration"

#: templates/wordpress_integration.html:34
msgid "Shortcode (Recommended)"
msgstr "Shortcode (Recommandé)"

#: templates/wordpress_integration.html:35
#: templates/wordpress_integration.html:187
msgid "WordPress Widget"
msgstr "Widget WordPress"

#: templates/wordpress_integration.html:36
#: templates/wordpress_integration.html:201
msgid "Custom Plugin"
msgstr "Plugin personnalisé"

#: templates/wordpress_integration.html:63
msgid "Design Settings"
msgstr "Paramètres de design"

#: templates/wordpress_integration.html:72
msgid "WordPress Theme Style"
msgstr "Style de thème WordPress"

#: templates/wordpress_integration.html:74
msgid "Inherit from Theme"
msgstr "Hériter du thème"

#: templates/wordpress_integration.html:84
msgid "Responsive Design"
msgstr "Design responsive"

#: templates/wordpress_integration.html:97
msgid "Jobs Per Page"
msgstr "Offres par page"

#: templates/wordpress_integration.html:109
msgid "Show Job Filters"
msgstr "Afficher les filtres d'emploi"

#: templates/wordpress_integration.html:116
msgid "Show Search Box"
msgstr "Afficher la boîte de recherche"

#: templates/wordpress_integration.html:123
msgid "Show Pagination"
msgstr "Afficher la pagination"

#: templates/wordpress_integration.html:132
msgid "Generate WordPress Code"
msgstr "Générer le code WordPress"

#: templates/wordpress_integration.html:141
msgid "Preview & Instructions"
msgstr "Aperçu & Instructions"

#: templates/wordpress_integration.html:144
msgid "Preview"
msgstr "Aperçu"

#: templates/wordpress_integration.html:147
msgid "Instructions"
msgstr "Instructions"

#: templates/wordpress_integration.html:167
msgid "Shortcode Integration"
msgstr "Intégration par shortcode"

#: templates/wordpress_integration.html:169
msgid "Recommended Method"
msgstr "Méthode recommandée"

#: templates/wordpress_integration.html:169
msgid "Easy to use and works with any WordPress theme"
msgstr "Facile à utiliser et fonctionne avec n'importe quel thème WordPress"

#: templates/wordpress_integration.html:172
msgid "Copy the shortcode below"
msgstr "Copiez le shortcode ci-dessous"

#: templates/wordpress_integration.html:173
msgid "Go to your WordPress admin panel"
msgstr "Allez dans votre panneau d'administration WordPress"

#: templates/wordpress_integration.html:174
msgid "Edit the page where you want to display jobs"
msgstr "Modifiez la page où vous voulez afficher les offres"

#: templates/wordpress_integration.html:175
msgid "Paste the shortcode in the content area"
msgstr "Collez le shortcode dans la zone de contenu"

#: templates/wordpress_integration.html:176
msgid "Save and publish the page"
msgstr "Enregistrez et publiez la page"

#: templates/wordpress_integration.html:189
msgid "Perfect for sidebars and widget areas"
msgstr "Parfait pour les barres latérales et zones de widgets"

#: templates/wordpress_integration.html:192
msgid "Go to Appearance > Widgets in your WordPress admin"
msgstr "Allez dans Apparence > Widgets dans votre admin WordPress"

#: templates/wordpress_integration.html:193
msgid "Find the 'Workloupe Careers' widget"
msgstr "Trouvez le widget 'Workloupe Careers'"

#: templates/wordpress_integration.html:194
msgid "Drag it to your desired widget area"
msgstr "Glissez-le dans votre zone de widgets souhaitée"

#: templates/wordpress_integration.html:195
msgid "Configure the widget settings"
msgstr "Configurez les paramètres du widget"

#: templates/wordpress_integration.html:196
msgid "Save the widget"
msgstr "Enregistrez le widget"

#: templates/wordpress_integration.html:203
msgid "Advanced option - requires technical knowledge"
msgstr "Option avancée - nécessite des connaissances techniques"

#: templates/wordpress_integration.html:206
msgid "Download the custom plugin file"
msgstr "Téléchargez le fichier du plugin personnalisé"

#: templates/wordpress_integration.html:207
msgid "Upload it to your WordPress plugins directory"
msgstr "Téléversez-le dans votre répertoire de plugins WordPress"

#: templates/wordpress_integration.html:208
msgid "Activate the plugin in WordPress admin"
msgstr "Activez le plugin dans l'admin WordPress"

#: templates/wordpress_integration.html:209
msgid "Configure the plugin settings"
msgstr "Configurez les paramètres du plugin"

#: templates/wordpress_integration.html:210
msgid "Use shortcodes or widgets as needed"
msgstr "Utilisez des shortcodes ou widgets au besoin"

#: templates/wordpress_integration.html:214
msgid "Download Plugin"
msgstr "Télécharger le plugin"

#: templates/wordpress_integration.html:231
msgid "WordPress Integration Code"
msgstr "Code d'intégration WordPress"

#: templates/wordpress_integration.html:238
msgid "Copy the code below and follow the integration instructions."
msgstr "Copiez le code ci-dessous et suivez les instructions d'intégration."

#: templates/wordpress_integration.html:243
msgid "WordPress Code"
msgstr "Code WordPress"

#: templates/wordpress_integration.html:256
msgid "Download Files"
msgstr "Télécharger les fichiers"

#: templates/workloupe_platform.html:13
msgid "Workloupe Platform Setup"
msgstr "Configuration de la plateforme Workloupe"

#: templates/workloupe_platform.html:14
msgid "Create and manage your company profile on workloupe.com"
msgstr "Créez et gérez votre profil d'entreprise sur workloupe.com"

#: templates/workloupe_platform.html:18
msgid ""
"Update your existing profile information below. All changes will be saved "
"automatically."
msgstr "Mettez à jour vos informations de profil existantes ci-dessous. Toutes les modifications seront enregistrées automatiquement."

#: templates/workloupe_platform.html:36
msgid "Company name is required"
msgstr "Le nom de l'entreprise est requis"

#: templates/workloupe_platform.html:41
msgid "Company Email"
msgstr "E-mail de l'entreprise"

#: templates/workloupe_platform.html:43
msgid "Please enter a valid email address"
msgstr "Veuillez entrer une adresse e-mail valide"

#: templates/workloupe_platform.html:51
msgid "Phone Number"
msgstr "Numéro de téléphone"

#: templates/workloupe_platform.html:53
msgid "Include country code (e.g., ******-123-4567)"
msgstr "Inclure l'indicatif pays (ex. +33 1 23 45 67 89)"

#: templates/workloupe_platform.html:58
msgid "Website"
msgstr "Site web"

#: templates/workloupe_platform.html:60
msgid "Please enter a valid website URL"
msgstr "Veuillez entrer une URL de site web valide"

#: templates/workloupe_platform.html:66
msgid "Company Address"
msgstr "Adresse de l'entreprise"

#: templates/workloupe_platform.html:67
msgid "Full company address including city, state, country"
msgstr "Adresse complète de l'entreprise incluant ville, région, pays"

#: templates/workloupe_platform.html:71
msgid "Office Locations"
msgstr "Emplacements des bureaux"

#: templates/workloupe_platform.html:72
msgid "Separate multiple locations with | (e.g., New York | London | Remote)"
msgstr "Séparez plusieurs emplacements par | (ex. Paris | Lyon | Télétravail)"

#: templates/workloupe_platform.html:73
msgid "Use | to separate multiple office locations"
msgstr "Utilisez | pour séparer plusieurs emplacements de bureau"

#: templates/workloupe_platform.html:77
msgid "Company Description"
msgstr "Description de l'entreprise"

#: templates/workloupe_platform.html:78
msgid ""
"Describe your company, mission, values, and what makes it special. This will "
"be prominently displayed on your profile."
msgstr "Décrivez votre entreprise, sa mission, ses valeurs et ce qui la rend spéciale. Ceci sera affiché en évidence sur votre profil."

#: templates/workloupe_platform.html:79
msgid "Company description is required"
msgstr "La description de l'entreprise est requise"

#: templates/workloupe_platform.html:80
msgid "Minimum 50 characters recommended for better visibility"
msgstr "Minimum 50 caractères recommandés pour une meilleure visibilité"

#: templates/workloupe_platform.html:88
msgid "Company Details"
msgstr "Détails de l'entreprise"

#: templates/workloupe_platform.html:94
msgid "Industry"
msgstr "Industrie"

#: templates/workloupe_platform.html:96
msgid "Select Industry"
msgstr "Sélectionnez l'industrie"

#: templates/workloupe_platform.html:97
msgid "Technology"
msgstr "Technologie"

#: templates/workloupe_platform.html:98
msgid "Healthcare"
msgstr "Santé"

#: templates/workloupe_platform.html:99
msgid "Finance"
msgstr "Finance"

#: templates/workloupe_platform.html:100
msgid "Education"
msgstr "Éducation"

#: templates/workloupe_platform.html:101
msgid "Manufacturing"
msgstr "Fabrication"

#: templates/workloupe_platform.html:102
msgid "Retail"
msgstr "Commerce de détail"

#: templates/workloupe_platform.html:103
msgid "Consulting"
msgstr "Conseil"

#: templates/workloupe_platform.html:104
msgid "Marketing & Advertising"
msgstr "Marketing & Publicité"

#: templates/workloupe_platform.html:105
msgid "Real Estate"
msgstr "Immobilier"

#: templates/workloupe_platform.html:106
msgid "Non-profit"
msgstr "Organisation à but non lucratif"

#: templates/workloupe_platform.html:113
msgid "Company Size"
msgstr "Taille de l'entreprise"

#: templates/workloupe_platform.html:114
msgid "Number of employees"
msgstr "Nombre d'employés"

#: templates/workloupe_platform.html:115
msgid "Please enter a valid number of employees"
msgstr "Veuillez entrer un nombre valide d'employés"

#: templates/workloupe_platform.html:116
msgid "Enter the total number of employees in your company"
msgstr "Entrez le nombre total d'employés dans votre entreprise"

#: templates/workloupe_platform.html:122
msgid "Social Media Links"
msgstr "Liens des réseaux sociaux"

#: templates/workloupe_platform.html:122
msgid "Optional"
msgstr "Optionnel"

#: templates/workloupe_platform.html:128
msgid "Please enter a valid LinkedIn URL"
msgstr "Veuillez entrer une URL LinkedIn valide"

#: templates/workloupe_platform.html:133
msgid "Please enter a valid Instagram URL"
msgstr "Veuillez entrer une URL Instagram valide"

#: templates/workloupe_platform.html:140
msgid "Please enter a valid Twitter URL"
msgstr "Veuillez entrer une URL Twitter valide"

#: templates/workloupe_platform.html:145
msgid "Please enter a valid Github URL"
msgstr "Veuillez entrer une URL Github valide"

#: templates/workloupe_platform.html:152
msgid "Please enter a valid Facebook URL"
msgstr "Veuillez entrer une URL Facebook valide"

#: templates/workloupe_platform.html:157
msgid "Please enter a valid Glassdoor URL"
msgstr "Veuillez entrer une URL Glassdoor valide"

#: templates/workloupe_platform.html:161
msgid ""
"Add your company's social media profiles to increase visibility. URLs will "
"be validated automatically."
msgstr "Ajoutez les profils de médias sociaux de votre entreprise pour augmenter la visibilité. Les URL seront validées automatiquement."

#: templates/workloupe_platform.html:169
msgid "Branding Assets"
msgstr "Éléments de marque"

#: templates/workloupe_platform.html:169
msgid "(Publicly Visible)"
msgstr "(Visible publiquement)"

#: templates/workloupe_platform.html:177
msgid "Recommended: 300x300px, PNG or JPG. Max 3MB."
msgstr "Recommandé : 300x300px, PNG ou JPG. Max 3MB."

#: templates/workloupe_platform.html:181
msgid "Current logo - upload new file to replace"
msgstr "Logo actuel - téléversez un nouveau fichier pour le remplacer"

#: templates/workloupe_platform.html:185 templates/workloupe_platform.html:1165
msgid "No logo uploaded"
msgstr "Aucun logo téléversé"

#: templates/workloupe_platform.html:193
msgid "Company Banner"
msgstr "Bannière de l'entreprise"

#: templates/workloupe_platform.html:195
msgid "Recommended: 1200x400px, PNG or JPG. Max 3MB."
msgstr "Recommandé : 1200x400px, PNG ou JPG. Max 3MB."

#: templates/workloupe_platform.html:199
msgid "Current banner - upload new file to replace"
msgstr "Bannière actuelle - téléversez un nouveau fichier pour la remplacer"

#: templates/workloupe_platform.html:203 templates/workloupe_platform.html:1172
msgid "No banner uploaded"
msgstr "Aucune bannière téléversée"

#: templates/workloupe_platform.html:213
msgid ""
"If you don't have online versions of your logo or banner, you can upload "
"them here. They will be stored securely and made publicly accessible for "
"your profile."
msgstr "Si vous n'avez pas de versions en ligne de votre logo ou bannière, vous pouvez les téléverser ici. Ils seront stockés en toute sécurité et rendus accessibles publiquement pour votre profil."

#: templates/workloupe_platform.html:221
msgid "Company Gallery"
msgstr "Galerie de l'entreprise"

#: templates/workloupe_platform.html:226
msgid "Important:"
msgstr "Important :"

#: templates/workloupe_platform.html:226
msgid ""
"Photos will be public once you publish your profile. Maximum 50 photos "
"allowed. We reserve the right to remove inappropriate content. Only upload "
"professional, work-appropriate images."
msgstr "Les photos seront publiques une fois votre profil publié. Maximum 50 photos autorisées. Nous nous réservons le droit de supprimer les contenus inappropriés. Ne téléversez que des images professionnelles et adaptées au travail."

#: templates/workloupe_platform.html:232
msgid "Current Photos"
msgstr "Photos actuelles"

#: templates/workloupe_platform.html:238
msgid "Loading existing photos..."
msgstr "Chargement des photos existantes..."

#: templates/workloupe_platform.html:246
msgid "Upload New Photos"
msgstr "Téléverser de nouvelles photos"

#: templates/workloupe_platform.html:249
msgid ""
"Select multiple photos (JPG, PNG, WebP). Max 5MB per photo. Showcase your "
"company culture, office, team, events, etc."
msgstr "Sélectionnez plusieurs photos (JPG, PNG, WebP). Max 5MB par photo. Mettez en valeur la culture de votre entreprise, bureau, équipe, événements, etc."

#: templates/workloupe_platform.html:260
msgid "Uploading photos..."
msgstr "Téléversement des photos..."

#: templates/workloupe_platform.html:268
msgid "Reset Form"
msgstr "Réinitialiser le formulaire"

#: templates/workloupe_platform.html:272
msgid "Save & Publish Profile"
msgstr "Enregistrer & Publier le profil"

#: templates/workloupe_platform.html:279
msgid ""
"Your data is secure and will only be used for your public company profile"
msgstr "Vos données sont sécurisées et ne seront utilisées que pour votre profil public d'entreprise"

#: templates/workloupe_platform.html:295
msgid "Profile Published Successfully!"
msgstr "Profil publié avec succès !"

#: templates/workloupe_platform.html:302
msgid "Your company is now live on Workloupe!"
msgstr "Votre entreprise est maintenant en ligne sur Workloupe !"

#: templates/workloupe_platform.html:303
msgid ""
"Your company profile has been created and published on workloupe.com. "
"Candidates can now discover your company and apply to your jobs."
msgstr "Votre profil d'entreprise a été créé et publié sur workloupe.com. Les candidats peuvent désormais découvrir votre entreprise et postuler à vos offres."

#: templates/workloupe_platform.html:307
msgid "Your Profile URL:"
msgstr "URL de votre profil :"

#: templates/workloupe_platform.html:321
msgid "Share Your Profile"
msgstr "Partagez votre profil"

#: templates/workloupe_platform.html:322
msgid "Share this link with candidates and on social media"
msgstr "Partagez ce lien avec les candidats et sur les réseaux sociaux"

#: templates/workloupe_platform.html:330
msgid "Update Anytime"
msgstr "Mettre à jour à tout moment"

#: templates/workloupe_platform.html:331
msgid "Return to this page to update your profile information"
msgstr "Revenez sur cette page pour mettre à jour vos informations de profil"

#: templates/workloupe_platform.html:341
msgid "View Live Profile"
msgstr "Voir le profil en direct"

#: templates/workloupe_platform.html:825
msgid "New logo selected - will be uploaded on save"
msgstr "Nouveau logo sélectionné - sera téléversé lors de l'enregistrement"

#: templates/workloupe_platform.html:842
msgid "New banner selected - will be uploaded on save"
msgstr "Nouvelle bannière sélectionnée - sera téléversée lors de l'enregistrement"

#: templates/workloupe_platform.html:850
msgid "Please select a valid image file (JPG, PNG, WebP)"
msgstr "Veuillez sélectionner un fichier image valide (JPG, PNG, WebP)"

#: templates/workloupe_platform.html:856
msgid "File size must be less than"
msgstr "La taille du fichier doit être inférieure à"

#: templates/workloupe_platform.html:868
msgid "No existing photos found"
msgstr "Aucune photo existante trouvée"

#: templates/workloupe_platform.html:889
msgid "Remove image"
msgstr "Supprimer l'image"

#: templates/workloupe_platform.html:919
msgid "Failed to remove image. Please try again."
msgstr "Échec de la suppression de l'image. Veuillez réessayer."

#: templates/workloupe_platform.html:924
msgid "An error occurred while removing the image."
msgstr "Une erreur s'est produite lors de la suppression de l'image."

#: templates/workloupe_platform.html:953
msgid "You can only upload"
msgstr "Vous ne pouvez téléverser que"

#: templates/workloupe_platform.html:953
msgid "more photos. Maximum"
msgstr "photos supplémentaires. Maximum"

#: templates/workloupe_platform.html:953
msgid "photos allowed."
msgstr "photos autorisées."

#: templates/workloupe_platform.html:1042
msgid "Please fix the validation errors before submitting."
msgstr "Veuillez corriger les erreurs de validation avant de soumettre."

#: templates/workloupe_platform.html:1097
msgid "Publishing..."
msgstr "Publication..."

#: templates/workloupe_platform.html:1119
msgid "Unknown error occurred"
msgstr "Une erreur inconnue s'est produite"

#: templates/workloupe_platform.html:1124
msgid "An error occurred while publishing your profile. Please try again."
msgstr "Une erreur s'est produite lors de la publication de votre profil. Veuillez réessayer."

#: templates/workloupe_platform.html:1153
msgid "Failed to copy URL. Please copy it manually."
msgstr "Échec de la copie de l'URL. Veuillez la copier manuellement."

#: templates/workloupe_platform.html:1158
msgid ""
"Are you sure you want to reset the form? All unsaved changes will be lost."
msgstr "Êtes-vous sûr de vouloir réinitialiser le formulaire ? Toutes les modifications non enregistrées seront perdues."