#!/usr/bin/env python
"""
Standalone script to create a new user with employee record and strong password.
This script can be run independently of Django's database configuration issues.

Usage:
    python create_user_script.py --firstname "<PERSON>" --lastname "<PERSON><PERSON>" --email "<EMAIL>" --company "Example Corp"
"""

import os
import sys
import secrets
import string
import argparse
import re
from urllib.parse import urlparse

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'canvider.settings')

# Temporarily unset DATABASE_URL to use SQLite for testing
if 'DATABASE_URL' in os.environ:
    del os.environ['DATABASE_URL']

import django
django.setup()

from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.db import transaction
from feed.models import Employee, Employer


class PasswordValidator:
    """Simple password validator for the standalone script"""
    
    MIN_LENGTH = 8
    MAX_LENGTH = 128
    
    @classmethod
    def validate_password_strength(cls, password):
        """Validate password strength according to security policies"""
        errors = []
        
        if len(password) < cls.MIN_LENGTH:
            errors.append(f"Password must be at least {cls.MIN_LENGTH} characters long.")
        
        if len(password) > cls.MAX_LENGTH:
            errors.append(f"Password must be no more than {cls.MAX_LENGTH} characters long.")
        
        if not re.search(r'[A-Z]', password):
            errors.append("Password must contain at least one uppercase letter.")
        
        if not re.search(r'[a-z]', password):
            errors.append("Password must contain at least one lowercase letter.")
        
        if not re.search(r'\d', password):
            errors.append("Password must contain at least one digit.")
        
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            errors.append("Password must contain at least one special character.")
        
        # Check for common passwords
        if cls.is_common_password(password):
            errors.append("Password is too common. Please choose a more unique password.")
        
        if errors:
            raise ValidationError(errors)
        
        return True
    
    @classmethod
    def is_common_password(cls, password):
        """Check if password is in common passwords list"""
        common_passwords = [
            'password', '123456', '123456789', 'qwerty', 'abc123',
            'password123', 'admin', 'letmein', 'welcome', 'monkey'
        ]
        return password.lower() in common_passwords


def generate_strong_password():
    """Generate a cryptographically strong password that meets all requirements"""
    # Define character sets
    uppercase = string.ascii_uppercase
    lowercase = string.ascii_lowercase
    digits = string.digits
    special_chars = '!@#$%^&*(),.?":{}|<>'
    
    # Ensure at least one character from each required set
    password_chars = [
        secrets.choice(uppercase),
        secrets.choice(lowercase), 
        secrets.choice(digits),
        secrets.choice(special_chars),
    ]
    
    # Fill the rest with random characters from all sets
    all_chars = uppercase + lowercase + digits + special_chars
    for _ in range(8):  # Total length will be 12 characters
        password_chars.append(secrets.choice(all_chars))
    
    # Shuffle the password to avoid predictable patterns
    secrets.SystemRandom().shuffle(password_chars)
    
    password = ''.join(password_chars)
    
    # Validate the generated password
    try:
        PasswordValidator.validate_password_strength(password)
        return password
    except ValidationError:
        # If somehow the generated password fails validation, try again
        return generate_strong_password()


def create_user(firstname, lastname, email, company, role='Administrator', custom_password=None):
    """Create a new user with employee record"""
    
    # Validate inputs
    if not firstname or not lastname or not email or not company:
        raise ValueError('All required fields (firstname, lastname, email, company) must be provided')

    if '@' not in email:
        raise ValueError('Invalid email address format')

    print('=== Creating New User Account ===')
    print(f'👤 Name: {firstname} {lastname}')
    print(f'📧 Email: {email}')
    print(f'🏢 Company: {company}')
    print(f'👔 Role: {role}')

    # Check if user already exists
    if User.objects.filter(email=email).exists():
        raise ValueError(f'User with email {email} already exists')

    # Generate or validate password
    if custom_password:
        password = custom_password
        try:
            PasswordValidator.validate_password_strength(password)
            print('🔐 Using provided password (validated)')
        except ValidationError as e:
            raise ValueError(f'Password validation failed: {", ".join(e.messages)}')
    else:
        password = generate_strong_password()
        print('🔐 Generated strong password')

    # Use database transaction to ensure atomicity
    with transaction.atomic():
        # Create or get employer
        employer, created = Employer.objects.get_or_create(
            employer_name=company,
            defaults={
                'employer_email': f'admin@{company.lower().replace(" ", "").replace(".", "")}.com',
                'employer_website': f'https://{company.lower().replace(" ", "").replace(".", "")}.com',
                'employer_description': f'{company} - Professional services company',
                'employer_status': 'Active',
            }
        )

        if created:
            print(f'✅ Created new employer: {employer.employer_name} (ID: {employer.employer_id})')
        else:
            print(f'📋 Using existing employer: {employer.employer_name} (ID: {employer.employer_id})')

        # Create username from email
        username = email.split('@')[0]
        
        # Ensure username is unique
        original_username = username
        counter = 1
        while User.objects.filter(username=username).exists():
            username = f"{original_username}{counter}"
            counter += 1

        # Create user
        user = User.objects.create_user(
            username=username,
            email=email,
            password=password,
            first_name=firstname,
            last_name=lastname,
            is_active=True,
        )
        print(f'✅ Created user: {user.username} ({user.email})')

        # Create employee
        employee = Employee.objects.create(
            user=user,
            employer_id=employer,
            role=role,
            status='Active',
        )
        print(f'✅ Created employee: {employee.role} - Status: {employee.status}')

    # Success summary
    print('\n=== User Account Created Successfully! ===')
    print('🔑 Login Credentials:')
    print(f'   Email: {email}')
    print(f'   Username: {username}')
    print(f'   Password: {password}')
    print(f'🏢 Company: {employer.employer_name}')
    print(f'👤 Role: {employee.role}')
    print(f'📊 Status: {employee.status}')
    
    print('\n⚠️  IMPORTANT SECURITY NOTES:')
    print('   • Send the password to the user through a secure channel')
    print('   • Advise the user to change their password on first login')
    print('   • Store these credentials securely and delete them after sharing')
    
    print('\n🌐 Login URL: /signin/')
    print('   The user can login with either their email or username')
    
    return {
        'email': email,
        'username': username,
        'password': password,
        'company': employer.employer_name,
        'role': employee.role
    }


def main():
    parser = argparse.ArgumentParser(description='Create a new user with employee record and strong password')
    parser.add_argument('--firstname', type=str, required=True, help='User first name')
    parser.add_argument('--lastname', type=str, required=True, help='User last name')
    parser.add_argument('--email', type=str, required=True, help='User email address')
    parser.add_argument('--company', type=str, required=True, help='Company/Employer name')
    parser.add_argument('--role', type=str, default='Administrator', 
                      choices=['Administrator', 'Hiring Manager', 'Interviewer', 'Read Only', 'Recruiter'],
                      help='Employee role (default: Administrator)')
    parser.add_argument('--password', type=str, help='Custom password (if not provided, a strong password will be generated)')

    args = parser.parse_args()

    try:
        result = create_user(
            firstname=args.firstname.strip(),
            lastname=args.lastname.strip(),
            email=args.email.strip().lower(),
            company=args.company.strip(),
            role=args.role,
            custom_password=args.password
        )
        return 0
    except Exception as e:
        print(f'\n❌ Error: {str(e)}')
        return 1


if __name__ == '__main__':
    sys.exit(main())
