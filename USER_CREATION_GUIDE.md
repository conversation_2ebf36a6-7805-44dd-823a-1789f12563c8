# User Creation Script

This directory contains a script to create new users with employee records and strong passwords for the Canvider ATS system.

## User Creation Script: `create_user_script.py`
**Location:** `create_user_script.py`

This is a standalone Python script that runs independently and has been tested to work correctly in production.

**Usage:**
```bash
python create_user_script.py --firstname "<PERSON>" --lastname "<PERSON><PERSON>" --email "<EMAIL>" --company "Example Corp"
```

**Options:**
- `--firstname` (required): User's first name
- `--lastname` (required): User's last name
- `--email` (required): User's email address
- `--company` (required): Company/Employer name
- `--role` (optional): Employee role (default: Administrator)
  - Choices: Administrator, Hiring Manager, Interviewer, Read Only, Recruiter
- `--password` (optional): Custom password (if not provided, a strong password will be generated)

**Examples:**
```bash
# Create user with auto-generated password
python create_user_script.py --firstname "<PERSON>" --lastname "<PERSON><PERSON>" --email "<EMAIL>" --company "Example Corp"

# Create user with specific role
python create_user_script.py --firstname "Jane" --lastname "Smith" --email "<EMAIL>" --company "My Company" --role "Recruiter"

# Create user with custom password
python create_user_script.py --firstname "Bob" --lastname "Wilson" --email "<EMAIL>" --company "Corp Inc" --password "MySecure123!"
```

## Features

### Strong Password Generation
The script generates cryptographically strong passwords that meet security requirements:
- At least 12 characters long
- Contains uppercase letters
- Contains lowercase letters
- Contains digits
- Contains special characters (!@#$%^&*(),.?":{}|<>)
- Not a common password
- No sequential characters

### Automatic Employer Creation
If the specified company doesn't exist, the script will automatically create a new Employer record with:
- Company name
- Generated email address
- Generated website URL
- Default description
- Active status

### Data Validation
- Email format validation
- Duplicate user detection
- Password strength validation (for custom passwords)
- Input sanitization

### Security Features
- Uses `secrets` module for cryptographically secure random generation
- Validates password strength against common vulnerabilities
- Provides security warnings and best practices
- Database transactions to ensure data consistency

## Output

The script provides detailed output including:
- User creation confirmation
- Generated credentials (email, username, password)
- Company information
- Role and status
- Security warnings
- Login instructions

**Example Output:**
```
=== Creating New User Account ===
👤 Name: John Doe
📧 Email: <EMAIL>
🏢 Company: Example Corp
👔 Role: Administrator
🔐 Generated strong password
✅ Created new employer: Example Corp (ID: 6)
✅ Created user: john.doe (<EMAIL>)
✅ Created employee: Administrator - Status: Active

=== User Account Created Successfully! ===
🔑 Login Credentials:
   Email: <EMAIL>
   Username: john.doe
   Password: 5NUfY}b1@>dW
🏢 Company: Example Corp
👤 Role: Administrator
📊 Status: Active

⚠️  IMPORTANT SECURITY NOTES:
   • Send the password to the user through a secure channel
   • Advise the user to change their password on first login
   • Store these credentials securely and delete them after sharing

🌐 Login URL: /signin/
   The user can login with either their email or username
```

## Security Best Practices

1. **Password Distribution**: Always send passwords through secure channels (encrypted email, secure messaging, etc.)
2. **First Login**: Advise users to change their password on first login
3. **Credential Storage**: Store credentials securely and delete them after sharing with the user
4. **Access Control**: Only authorized administrators should run these scripts
5. **Audit Trail**: Keep logs of user creation activities

## Troubleshooting

### Permission Errors
Ensure you have the necessary database permissions to create users and employers.

### Duplicate Email Error
If a user with the same email already exists, the script will fail. Check existing users first or use a different email address.

### Database Connection Issues
If you encounter any database connection issues, check your environment variables and database configuration.

## Integration with Existing System

The created users will:
- Be able to log in immediately with the provided credentials
- Have access to the system based on their assigned role
- Be properly isolated to their employer's data
- Follow all existing security and authentication policies
- Be subject to the same password change requirements as other users
