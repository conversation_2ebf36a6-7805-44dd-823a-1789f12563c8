{% extends 'main.html' %} {% load static %} {% load i18n %} {% block content%}
<div class="container">
  <h1>{% trans "Create Job Position" %}</h1>
  <div class="form-container">
    <div class="form-row">
      <!-- First Column -->
      <div class="form-column">
        <h2>{% trans "Basic Information" %}</h2>
        <div class="form-group">
          <label for="position-title">{% trans "Role Title" %}</label>
          <input
            type="text"
            id="position-title"
            placeholder="{% trans 'e.g. Senior Software Engineer' %}"
          />
        </div>
        <div class="form-group">
          <label for="office-location">{% trans "Office Location" %}</label>
          {% if has_locations %}
          <select id="office-location">
            <option value="">{% trans "Select office location" %}</option>
            {% for location in office_locations %}
            <option value="{{ location.city }}, {{ location.country }}">
              {{ location.city }}, {{ location.country }}
            </option>
            {% endfor %}
          </select>
          {% else %}
          <div class="no-data-message">
            <p>
              {% trans "No office locations found. Please" %}
              <a href="{% url 'preferences' %}">{% trans "add office locations" %}</a> {% trans "in your preferences first." %}
            </p>
          </div>
          <select id="office-location" disabled>
            <option value="">{% trans "No locations available" %}</option>
          </select>
          {% endif %}
        </div>
        <div class="form-group">
          <label for="work-schedule">{% trans "Work Schedule" %}</label>
          {% if has_work_schedules %}
          <select id="work-schedule">
            <option value="">{% trans "Select an option" %}</option>
            {% for schedule in work_schedules %}
            <option value="{{ schedule.name }}">{{ schedule.name }}</option>
            {% endfor %}
          </select>
          {% else %}
          <div class="no-data-message">
            <p>
              {% trans "No work schedules found. Please" %}
              <a href="{% url 'preferences' %}">{% trans "add work schedules" %}</a> {% trans "in your preferences first." %}
            </p>
          </div>
          <select id="work-schedule" disabled>
            <option value="">{% trans "No work schedules available" %}</option>
          </select>
          {% endif %}
        </div>
        <div class="form-group">
          <label for="office-schedule">{% trans "Office Schedule" %}</label>
          {% if has_office_schedules %}
          <select id="office-schedule">
            <option value="">{% trans "Select an option" %}</option>
            {% for schedule in office_schedules %}
            <option value="{{ schedule.name }}">{{ schedule.name }}</option>
            {% endfor %}
          </select>
          {% else %}
          <div class="no-data-message">
            <p>
              {% trans "No office schedules found. Please" %}
              <a href="{% url 'preferences' %}">{% trans "add office schedules" %}</a> {% trans "in your preferences first." %}
            </p>
          </div>
          <select id="office-schedule" disabled>
            <option value="">{% trans "No office schedules available" %}</option>
          </select>
          {% endif %}
        </div>
        <div class="form-group">
          <label for="department">{% trans "Department" %}</label>
          {% if has_departments %}
          <select id="department">
            <option value="">{% trans "Select a department" %}</option>
            {% for department in departments %}
            <option value="{{ department.name }}">{{ department.name }}</option>
            {% endfor %}
          </select>
          {% else %}
          <div class="no-data-message">
            <p>
              {% trans "No departments found. Please" %}
              <a href="{% url 'preferences' %}">{% trans "add departments" %}</a> {% trans "in your preferences first." %}
            </p>
          </div>
          <select id="department" disabled>
            <option value="">{% trans "No departments available" %}</option>
          </select>
          {% endif %}
        </div>
      </div>

    </div>

    <div class="next-btn-container">
      <button class="discard-btn mx-2">{% trans "Discard" %}</button>
      <button class="next-btn" id="next-btn">{% trans "Next" %}</button>
    </div>
  </div>
</div>

<style>
  :root {
    --primary: #4a6cf7;
    --primary-hover: #3859e9;
    --secondary: #f5f8ff;
    --text-color: #333;
    --border-color: #ddd;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  }

  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  }

  body {
    background-color: #f9fafc;
    color: var(--text-color);
    line-height: 1.6;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
  }

  h1,
  h2 {
    margin-bottom: 20px;
    color: #252b42;
  }

  h1 {
    font-size: 28px;
  }

  h2 {
    font-size: 20px;
    font-weight: 600;
  }

  .form-container {
    background-color: white;
    border-radius: 8px;
    padding: 30px;
    box-shadow: var(--shadow);
  }

  .form-row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
  }

  .form-column {
    flex: 1;
    padding: 0 15px;
    min-width: 300px;
  }

  .form-group {
    margin-bottom: 24px;
  }

  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    font-size: 14px;
  }

  input,
  select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s;
  }

  input:focus,
  select:focus {
    outline: none;
    border-color: var(--primary);
  }



  .next-btn-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
  }



  .next-btn {
    background-color: var(--primary);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .discard-btn {
    background-color: white;
    color: black;
    border: 1px solid rgb(100, 24, 34);
    padding: 12px 30px;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
  }
  .discard-btn:hover {
    background-color: rgb(100, 24, 34);
    color: white;
  }

  .next-btn:hover {
    background-color: var(--primary-hover);
  }

  .empty-message {
    color: #999;
    font-style: italic;
    text-align: center;
    padding: 20px;
  }
  .error-message {
    color: #e63946;
    font-size: 12px;
    margin-top: 5px;
  }

  .input-error {
    border-color: #e63946 !important;
  }

  .no-data-message {
    background-color: #fff3cd;
    color: #856404;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 10px;
    font-size: 14px;
  }

  .no-data-message a {
    color: #0056b3;
    text-decoration: underline;
    font-weight: 600;
  }

  .no-data-message a:hover {
    text-decoration: none;
  }


</style>

<script>
  document.addEventListener("DOMContentLoaded", function () {

    // Function to display validation error messages
    function showError(inputElement, message) {
      // Remove any existing error message
      const existingError =
        inputElement.parentElement.querySelector(".error-message");
      if (existingError) existingError.remove();

      // Create and add error message
      const errorElement = document.createElement("div");
      errorElement.className = "error-message";
      errorElement.textContent = message;
      inputElement.parentElement.appendChild(errorElement);

      // Highlight the input field
      inputElement.classList.add("input-error");
    }

    // Function to clear validation error
    function clearError(inputElement) {
      const errorElement =
        inputElement.parentElement.querySelector(".error-message");
      if (errorElement) errorElement.remove();
      inputElement.classList.remove("input-error");
    }

    // Function to validate the form
    function validateForm() {
      let isValid = true;

      // Validate Position Title
      const positionTitle = document.getElementById("position-title");
      if (!positionTitle.value.trim()) {
        showError(positionTitle, "Role Title is required");
        isValid = false;
      } else {
        clearError(positionTitle);
      }

      // Validate Office Location
      const officeLocation = document.getElementById("office-location");
      if (!officeLocation.disabled && !officeLocation.value) {
        showError(officeLocation, "Office Location is required");
        isValid = false;
      } else if (officeLocation.disabled) {
        showError(
          officeLocation,
          "Please add office locations in your preferences first"
        );
        isValid = false;
      } else {
        clearError(officeLocation);
      }

      // Validate Work Schedule
      const workSchedule = document.getElementById("work-schedule");
      if (!workSchedule.disabled && !workSchedule.value) {
        showError(workSchedule, "Work Schedule is required");
        isValid = false;
      } else if (workSchedule.disabled) {
        showError(
          workSchedule,
          "Please add work schedules in your preferences first"
        );
        isValid = false;
      } else {
        clearError(workSchedule);
      }

      // Validate Office Schedule
      const officeSchedule = document.getElementById("office-schedule");
      if (!officeSchedule.disabled && !officeSchedule.value) {
        showError(officeSchedule, "Office Schedule is required");
        isValid = false;
      } else if (officeSchedule.disabled) {
        showError(
          officeSchedule,
          "Please add office schedules in your preferences first"
        );
        isValid = false;
      } else {
        clearError(officeSchedule);
      }

      return isValid;
    }

    // Event listener for Next button
    document.getElementById("next-btn").addEventListener("click", function () {
      // Validate the form first
      if (!validateForm()) {
        return; // Stop if validation fails
      }

      // Collect basic information data
      const formData = {
        roleTitle: document.getElementById("position-title").value,
        officeLocation: document.getElementById("office-location").value,
        workSchedule: document.getElementById("work-schedule").value,
        officeSchedule: document.getElementById("office-schedule").value,
        department: document.getElementById("department").value,
      };

      sessionStorage.setItem("jobBasicInfo", JSON.stringify(formData));

      // Navigate to the skills and requirements page
      window.location.href = "{% url 'job_requirements' %}";
    });

    // Add input event listeners to clear errors when user starts typing
    document
      .getElementById("position-title")
      .addEventListener("input", function () {
        clearError(this);
      });

    document
      .getElementById("office-location")
      .addEventListener("input", function () {
        clearError(this);
      });

    document
      .getElementById("work-schedule")
      .addEventListener("change", function () {
        clearError(this);
      });

    document
      .getElementById("office-schedule")
      .addEventListener("change", function () {
        clearError(this);
      });
  });
</script>
{% endblock %}
