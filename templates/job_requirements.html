{% extends 'main.html' %} {% load static %} {% load i18n %} {% block content%}
<div class="container">
  <h1>{% trans "Job Requirements & Details" %}</h1>
  
  <!-- Basic Information Summary -->
  <div class="summary-container">
    <h2>{% trans "Basic Information" %}</h2>
    <div class="summary-grid" id="basic-info-summary">
      <!-- Data will be loaded here from sessionStorage -->
      <div class="summary-loading">{% trans "Loading job details..." %}</div>
    </div>
  </div>

  <!-- AI Suggestion Container -->
  <div class="ai-suggestion-container">
    <h2>{% trans "AI-Powered Suggestions" %}</h2>
    <p class="ai-hint">{% trans "Let AI suggest skills and salary range based on your job title and location." %}</p>
    <button class="ai-suggest-btn" id="ai-suggest-btn">
      <i class="fas fa-robot"></i>
      {% trans "Get AI Suggestions" %}
    </button>
  </div>

  <div class="form-container">
    <div class="form-row">
      <!-- Skills Requirements Column -->
      <div class="form-column">
        <h2>{% trans "Skills Requirements" %}</h2>
        <div class="skills-container">
          <label for="role-title">{% trans "Skill" %}</label>
          <div class="skills-input-group">
            <input
              type="text"
              id="skills-input"
              placeholder="{% trans 'e.g. JavaScript' %}"
            />
            <button class="add-btn" id="add-skill-btn">{% trans "Add" %}</button>
          </div>

          <label>{% trans "Choose Skills" %}</label>
          <div class="skill-chips">
            <div class="skill-chip" data-skill="JavaScript">JavaScript</div>
            <div class="skill-chip" data-skill="Python">Python</div>
            <div class="skill-chip" data-skill="Java">Java</div>
            <div class="skill-chip" data-skill="C++">C++</div>
            <div class="skill-chip" data-skill="C#">C#</div>
            <div class="skill-chip" data-skill="React">React</div>
            <div class="skill-chip" data-skill="Node.js">Node.js</div>
            <div class="skill-chip" data-skill="SQL">SQL</div>
            <div class="skill-chip" data-skill="AWS">AWS</div>
            <div class="skill-chip" data-skill="Docker">Docker</div>
          </div>

          <label>{% trans "Selected Skills" %}</label>
          <div class="selected-skills" id="selected-skills">
            <div class="empty-message" id="empty-skills-message">
              {% trans "No skills selected yet" %}
            </div>
            <!-- Selected skills will appear here -->
          </div>
        </div>
      </div>

      <!-- Salary Details Column -->
      <div class="form-column">
        <h2>{% trans "Salary Details (Optional)" %}</h2>
        <div class="salary-grid">
          <div class="form-group">
            <label for="salary-min">{% trans "Minimum Salary" %}</label>
            <input
              type="number"
              id="salary-min"
              placeholder="{% trans 'Enter minimum salary' %}"
            />
          </div>
          <div class="form-group">
            <label for="salary-max">{% trans "Maximum Salary" %}</label>
            <input
              type="number"
              id="salary-max"
              placeholder="{% trans 'Enter maximum salary' %}"
            />
          </div>
          <div class="form-group">
            <label for="salary-currency">{% trans "Currency" %}</label>
            <select id="salary-currency">
              <option value="">{% trans "Select currency" %}</option>
              <option value="USD">USD</option>
              <option value="EUR">EUR</option>
              <option value="PLN">PLN</option>
              <option value="TRY">TRY</option>
              <option value="GBP">GBP</option>
              <option value="JPY">JPY</option>
              <option value="AUD">AUD</option>
            </select>
          </div>
        </div>
      </div>
    </div>
    <br>
    <!-- Job Benefits & Highlights -->
    <div class="benefit-container">
      <h2 for="benefits-input">{% trans "Benefits and Highlights (Optional)" %}</h2>
      <div class="skills-input-group">
        <input
          type="text"
          id="benefits-input"
          placeholder="{% trans 'e.g. Yearly Bonuses' %}"
        />
        <button class="add-btn" id="add-benefit-btn">{% trans "Add" %}</button>
      </div>

      <label>{% trans "Choose Benefits" %}</label>
      <div class="benefit-chips">
        <div class="benefit-chip" data-benefit="Dental Coverage">
          {% trans "Dental Coverage" %}
        </div>
        <div class="benefit-chip" data-benefit="Private Health Coverage">
          {% trans "Private Health Coverage" %}
        </div>
        <div class="benefit-chip" data-benefit="Gym membership">
          {% trans "Gym membership" %}
        </div>
        <div class="benefit-chip" data-benefit="Sign-in Bonus">
          {% trans "Sign-in Bonus" %}
        </div>
        <div class="benefit-chip" data-benefit="Relocation Package">
          {% trans "Relocation Package" %}
        </div>
        <div class="benefit-chip" data-benefit="Company Vehicle">
          {% trans "Company Vehicle" %}
        </div>
        <div class="benefit-chip" data-benefit="Food Card">{% trans "Food Card" %}</div>
        <div class="benefit-chip" data-benefit="Snacks & Coffee">
          {% trans "Snacks & Coffee" %}
        </div>
        <div class="benefit-chip" data-benefit="Pet Friendly Office">
          {% trans "Pet Friendly Office" %}
        </div>
      </div>

      <label>{% trans "Selected Benefits & Highlights" %}</label>
      <div class="selected-benefits" id="selected-benefits">
        <div class="empty-message" id="empty-benefits-message">
          {% trans "No benefits or highlights selected yet" %}
        </div>
        <!-- Selected benefits will appear here -->
      </div>
    </div>

    <div class="next-btn-container">
      <button class="back-btn" id="back-btn">{% trans "Back" %}</button>
      <button class="discard-btn mx-2">{% trans "Discard" %}</button>
      <button class="next-btn" id="next-btn">{% trans "Next" %}</button>
    </div>
  </div>
</div>

<!-- Loading overlay for AI suggestions -->
<div class="ai-loading-overlay" id="ai-loading-overlay" style="display: none;">
  <div class="ai-loading-content">
    <div class="ai-loading-spinner"></div>
    <div class="ai-loading-text">{% trans "AI is analyzing your job..." %}</div>
    <div class="ai-loading-subtext">{% trans "Generating skills and salary suggestions" %}</div>
  </div>
</div>

<style>
  :root {
    --primary: #4a6cf7;
    --primary-hover: #3859e9;
    --secondary: #f5f8ff;
    --text-color: #333;
    --border-color: #ddd;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  }

  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  }

  body {
    background-color: #f9fafc;
    color: var(--text-color);
    line-height: 1.6;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
  }

  h1,
  h2 {
    margin-bottom: 20px;
    color: #252b42;
  }

  h1 {
    font-size: 28px;
  }

  h2 {
    font-size: 20px;
    font-weight: 600;
  }

  /* Summary Container */
  .summary-container {
    background-color: white;
    border-radius: 8px;
    padding: 30px;
    box-shadow: var(--shadow);
    margin-bottom: 30px;
  }

  .summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }

  .summary-item {
    padding: 15px;
    background-color: var(--secondary);
    border-radius: 6px;
  }

  .summary-label {
    font-weight: 600;
    font-size: 14px;
    color: #555;
    margin-bottom: 5px;
  }

  .summary-value {
    font-size: 16px;
  }

  .summary-loading {
    grid-column: 1 / -1;
    text-align: center;
    padding: 20px;
    color: #888;
    font-style: italic;
  }

  /* AI Suggestion Container */
  .ai-suggestion-container {
    background-color: white;
    border-radius: 8px;
    padding: 30px;
    box-shadow: var(--shadow);
    margin-bottom: 30px;
    border: 2px dashed var(--primary);
    background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
  }

  .ai-hint {
    color: #666;
    margin-bottom: 20px;
    font-size: 15px;
  }

  .ai-suggest-btn {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-hover) 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: 0 4px 15px rgba(74, 108, 247, 0.3);
  }

  .ai-suggest-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(74, 108, 247, 0.4);
  }

  .ai-suggest-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  .ai-suggest-btn i {
    font-size: 18px;
  }

  /* Loading overlay for AI suggestions */
  .ai-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1001;
  }

  .ai-loading-content {
    background-color: white;
    padding: 40px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 400px;
  }

  .ai-loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .ai-loading-text {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
  }

  .ai-loading-subtext {
    font-size: 14px;
    color: #666;
  }

  .form-container {
    background-color: white;
    border-radius: 8px;
    padding: 30px;
    box-shadow: var(--shadow);
  }

  .form-row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
  }

  .form-column {
    flex: 1;
    padding: 0 15px;
    min-width: 300px;
  }

  .form-group {
    margin-bottom: 24px;
  }

  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    font-size: 14px;
  }

  input,
  select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s;
  }

  input:focus,
  select:focus {
    outline: none;
    border-color: var(--primary);
  }

  .skills-container {
    margin-top: 20px;
  }

  .skills-input-group {
    display: flex;
    margin-bottom: 16px;
  }

  .skills-input-group input {
    flex: 1;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .add-btn {
    background-color: var(--primary);
    color: white;
    border: none;
    padding: 0 20px;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .add-btn:hover {
    background-color: var(--primary-hover);
  }

  .skill-chips {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
  }

  .skill-chip {
    background-color: var(--secondary);
    padding: 8px 16px;
    border-radius: 30px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s, color 0.3s;
  }

  .skill-chip:hover {
    background-color: var(--primary);
    color: white;
  }

  .selected-skills {
    min-height: 100px;
    border: 1px dashed var(--border-color);
    border-radius: 6px;
    padding: 16px;
    margin-top: 16px;
    background-color: #fafafa;
  }

  .selected-skill {
    display: inline-flex;
    align-items: center;
    background-color: var(--primary);
    color: white;
    padding: 6px 12px;
    border-radius: 30px;
    margin: 0 8px 8px 0;
    font-size: 14px;
  }

  .remove-skill {
    margin-left: 8px;
    cursor: pointer;
    font-size: 16px;
  }

  /* Salary Container Styling */
  .salary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }

  .form-group {
    display: flex;
    flex-direction: column;
  }

  .form-group label {
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 14px;
  }

  .form-group input,
  .form-group select {
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s;
  }

  .form-group input:focus,
  .form-group select:focus {
    outline: none;
    border-color: var(--primary);
  }

  .selected-benefits {
    min-height: 100px;
    border: 1px dashed var(--border-color);
    border-radius: 6px;
    padding: 16px;
    margin-top: 16px;
    background-color: #fafafa;
  }

  .selected-benefit {
    display: inline-flex;
    align-items: center;
    background-color: rgb(44, 44, 44);
    color: white;
    padding: 6px 12px;
    border-radius: 30px;
    margin: 0 8px 8px 0;
    font-size: 14px;
  }

  .remove-benefit {
    margin-left: 8px;
    cursor: pointer;
    font-size: 16px;
  }

  .benefit-chips {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
  }

  .benefit-chip {
    background-color: var(--secondary);
    padding: 8px 16px;
    border-radius: 30px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s, color 0.3s;
  }

  .benefit-chip:hover {
    background-color: rgb(44, 44, 44);
    color: white;
  }

  .next-btn-container {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
  }

  .next-btn, .back-btn, .discard-btn {
    padding: 12px 30px;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .next-btn {
    background-color: var(--primary);
    color: white;
    border: none;
  }

  .next-btn:hover {
    background-color: var(--primary-hover);
  }

  .back-btn {
    background-color: white;
    color: var(--text-color);
    border: 1px solid var(--border-color);
  }

  .back-btn:hover {
    background-color: #f5f5f5;
  }

  .discard-btn {
    background-color: white;
    color: black;
    border: 1px solid rgb(100, 24, 34);
  }

  .discard-btn:hover {
    background-color: rgb(100, 24, 34);
    color: white;
  }

  .empty-message {
    color: #999;
    font-style: italic;
    text-align: center;
    padding: 20px;
  }
</style>

<!-- Include Font Awesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

<script>
  document.addEventListener("DOMContentLoaded", function () {
    // Load basic information from sessionStorage
    loadBasicInfoSummary();

    // Initialize skills functionality
    initSkillsManagement();

    // Initialize benefits functionality
    initBenefitsManagement();

    // Initialize AI suggestions
    initAISuggestions();

    // Initialize navigation buttons
    initNavigationButtons();
  });

  function loadBasicInfoSummary() {
    const summaryContainer = document.getElementById('basic-info-summary');
    const basicInfo = JSON.parse(sessionStorage.getItem('jobBasicInfo'));

    if (!basicInfo) {
      summaryContainer.innerHTML = `
        <div class="summary-item" style="grid-column: 1 / -1;">
          <p>{% trans "No basic information found. Please go back and fill out the basic information form." %}</p>
        </div>
      `;
      return;
    }

    // Clear loading message
    summaryContainer.innerHTML = '';

    // Create summary items for each field
    const summaryHTML = `
      <div class="summary-item">
        <div class="summary-label">{% trans "Role Title" %}</div>
        <div class="summary-value">${basicInfo.roleTitle}</div>
      </div>
      <div class="summary-item">
        <div class="summary-label">{% trans "Office Location" %}</div>
        <div class="summary-value">${basicInfo.officeLocation}</div>
      </div>
      <div class="summary-item">
        <div class="summary-label">{% trans "Work Schedule" %}</div>
        <div class="summary-value">${formatSchedule(basicInfo.workSchedule)}</div>
      </div>
      <div class="summary-item">
        <div class="summary-label">{% trans "Office Schedule" %}</div>
        <div class="summary-value">${formatSchedule(basicInfo.officeSchedule)}</div>
      </div>
      <div class="summary-item">
        <div class="summary-label">{% trans "Department" %}</div>
        <div class="summary-value">${basicInfo.department || 'Not specified'}</div>
      </div>
    `;

    summaryContainer.innerHTML = summaryHTML;
  }

  // Format schedule values for display
  function formatSchedule(value) {
    if (!value) return 'Not specified';

    // Convert kebab-case to Title Case with spaces
    return value
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  function initSkillsManagement() {
    const skillsInput = document.getElementById("skills-input");
    const addSkillBtn = document.getElementById("add-skill-btn");
    const selectedSkillsContainer = document.getElementById("selected-skills");
    const emptySkillsMessage = document.getElementById("empty-skills-message");
    const skillChips = document.querySelectorAll(".skill-chip");

    // Array to store selected skills
    let selectedSkills = [];

    // Function to add a skill
    function addSkill(skill) {
      if (!skill || selectedSkills.includes(skill)) return;

      selectedSkills.push(skill);
      updateSelectedSkillsDisplay();
      skillsInput.value = "";
    }

    // Function to remove a skill
    function removeSkill(skill) {
      selectedSkills = selectedSkills.filter((s) => s !== skill);
      updateSelectedSkillsDisplay();
    }

    // Function to update the selected skills display
    function updateSelectedSkillsDisplay() {
      if (selectedSkills.length === 0) {
        emptySkillsMessage.style.display = "block";
        selectedSkillsContainer.innerHTML = "";
        selectedSkillsContainer.appendChild(emptySkillsMessage);
        return;
      }

      emptySkillsMessage.style.display = "none";

      // Clear container first
      selectedSkillsContainer.innerHTML = "";

      // Add each skill as a chip
      selectedSkills.forEach((skill) => {
        const skillElement = document.createElement("div");
        skillElement.className = "selected-skill";
        skillElement.innerHTML = `
          ${skill}
          <span class="remove-skill" data-skill="${skill}">&times;</span>
        `;
        selectedSkillsContainer.appendChild(skillElement);
      });

      // Add event listeners to remove buttons
      document.querySelectorAll(".remove-skill").forEach((btn) => {
        btn.addEventListener("click", function () {
          removeSkill(this.getAttribute("data-skill"));
        });
      });
    }

    // Event listener for Add button
    addSkillBtn.addEventListener("click", function () {
      const skill = skillsInput.value.trim();
      addSkill(skill);
    });

    // Event listener for Enter key in input
    skillsInput.addEventListener("keypress", function (e) {
      if (e.key === "Enter") {
        const skill = this.value.trim();
        addSkill(skill);
      }
    });

    // Event listeners for skill chips
    skillChips.forEach((chip) => {
      chip.addEventListener("click", function () {
        const skill = this.getAttribute("data-skill");
        addSkill(skill);
      });
    });

    // Make selectedSkills available globally
    window.getSelectedSkills = () => selectedSkills;
    window.setSelectedSkills = (skills) => {
      selectedSkills = skills;
      updateSelectedSkillsDisplay();
    };
  }

  function initBenefitsManagement() {
    const benefitsInput = document.getElementById("benefits-input");
    const addBenefitBtn = document.getElementById("add-benefit-btn");
    const selectedBenefitsContainer = document.getElementById("selected-benefits");
    const emptyBenefitsMessage = document.getElementById("empty-benefits-message");
    const benefitChips = document.querySelectorAll(".benefit-chip");

    // Array to store selected benefits
    let selectedBenefits = [];

    // Function to add a benefit
    function addBenefit(benefit) {
      if (!benefit || selectedBenefits.includes(benefit)) return;

      selectedBenefits.push(benefit);
      updateSelectedBenefitsDisplay();
      benefitsInput.value = "";
    }

    // Function to remove a benefit
    function removeBenefit(benefit) {
      selectedBenefits = selectedBenefits.filter((b) => b !== benefit);
      updateSelectedBenefitsDisplay();
    }

    // Function to update the selected benefits display
    function updateSelectedBenefitsDisplay() {
      if (selectedBenefits.length === 0) {
        emptyBenefitsMessage.style.display = "block";
        selectedBenefitsContainer.innerHTML = "";
        selectedBenefitsContainer.appendChild(emptyBenefitsMessage);
        return;
      }

      emptyBenefitsMessage.style.display = "none";

      // Clear container first
      selectedBenefitsContainer.innerHTML = "";

      // Add each benefit as a chip
      selectedBenefits.forEach((benefit) => {
        const benefitElement = document.createElement("div");
        benefitElement.className = "selected-benefit";
        benefitElement.innerHTML = `
          ${benefit}
          <span class="remove-benefit" data-benefit="${benefit}">&times;</span>
        `;
        selectedBenefitsContainer.appendChild(benefitElement);
      });

      // Add event listeners to remove buttons
      document.querySelectorAll(".remove-benefit").forEach((btn) => {
        btn.addEventListener("click", function () {
          removeBenefit(this.getAttribute("data-benefit"));
        });
      });
    }

    // Event listener for Add button
    addBenefitBtn.addEventListener("click", function () {
      const benefit = benefitsInput.value.trim();
      addBenefit(benefit);
    });

    // Event listener for Enter key in input
    benefitsInput.addEventListener("keypress", function (e) {
      if (e.key === "Enter") {
        const benefit = this.value.trim();
        addBenefit(benefit);
      }
    });

    // Event listeners for benefit chips
    benefitChips.forEach((chip) => {
      chip.addEventListener("click", function () {
        const benefit = this.getAttribute("data-benefit");
        addBenefit(benefit);
      });
    });

    // Make selectedBenefits available globally
    window.getSelectedBenefits = () => selectedBenefits;
    window.setSelectedBenefits = (benefits) => {
      selectedBenefits = benefits;
      updateSelectedBenefitsDisplay();
    };
  }

  function initAISuggestions() {
    const aiSuggestBtn = document.getElementById('ai-suggest-btn');
    const loadingOverlay = document.getElementById('ai-loading-overlay');

    aiSuggestBtn.addEventListener('click', function() {
      const basicInfo = JSON.parse(sessionStorage.getItem('jobBasicInfo'));

      if (!basicInfo || !basicInfo.roleTitle || !basicInfo.officeLocation) {
        alert('{% trans "Please ensure role title and location are provided for AI suggestions." %}');
        return;
      }

      // Show loading overlay
      loadingOverlay.style.display = 'flex';
      aiSuggestBtn.disabled = true;

      // Prepare data for AI suggestion
      const suggestionData = {
        roleTitle: basicInfo.roleTitle,
        location: basicInfo.officeLocation,
        department: basicInfo.department || '',
        workSchedule: basicInfo.workSchedule || '',
        officeSchedule: basicInfo.officeSchedule || ''
      };

      // Call AI suggestion API
      fetch('/ai/suggest_job_requirements/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': getCSRFToken()
        },
        body: JSON.stringify(suggestionData)
      })
      .then(response => {
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        return response.json();
      })
      .then(data => {
        if (data.success) {
          // Apply AI suggestions
          if (data.skills && data.skills.length > 0) {
            window.setSelectedSkills(data.skills);
          }

          if (data.salary) {
            if (data.salary.min) {
              document.getElementById('salary-min').value = data.salary.min;
            }
            if (data.salary.max) {
              document.getElementById('salary-max').value = data.salary.max;
            }
            if (data.salary.currency) {
              document.getElementById('salary-currency').value = data.salary.currency;
            }
          }

          if (data.benefits && data.benefits.length > 0) {
            window.setSelectedBenefits(data.benefits);
          }

          alert('{% trans "AI suggestions applied successfully!" %}');
        } else {
          throw new Error(data.message || 'Failed to get AI suggestions');
        }
      })
      .catch(error => {
        console.error('AI suggestion error:', error);
        alert('{% trans "Error getting AI suggestions. Please try again or fill manually." %}');
      })
      .finally(() => {
        // Hide loading overlay
        loadingOverlay.style.display = 'none';
        aiSuggestBtn.disabled = false;
      });
    });
  }

  function initNavigationButtons() {
    // Back button
    document.getElementById('back-btn').addEventListener('click', function() {
      window.history.back();
    });

    // Discard button
    document.querySelector('.discard-btn').addEventListener('click', function() {
      if (confirm('{% trans "Are you sure you want to discard all changes?" %}')) {
        sessionStorage.removeItem('jobBasicInfo');
        window.location.href = "{% url 'feed' %}";
      }
    });

    // Next button
    document.getElementById('next-btn').addEventListener('click', function() {
      // Collect all requirements data
      const requirementsData = {
        skills: window.getSelectedSkills(),
        salaryMin: document.getElementById("salary-min").value || 0,
        salaryMax: document.getElementById("salary-max").value || 0,
        salaryCurrency: document.getElementById("salary-currency").value || "",
        benefits: window.getSelectedBenefits(),
      };

      // Get basic info and merge with requirements
      const basicInfo = JSON.parse(sessionStorage.getItem('jobBasicInfo'));
      const completeJobData = {
        ...basicInfo,
        ...requirementsData
      };

      // Save complete job data
      sessionStorage.setItem("jobFormData", JSON.stringify(completeJobData));

      // Navigate to description page
      window.location.href = "{% url 'description' %}";
    });
  }

  // Helper function to get CSRF token
  function getCSRFToken() {
    const name = 'csrftoken';
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
      const cookies = document.cookie.split(';');
      for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        if (cookie.substring(0, name.length + 1) === (name + '=')) {
          cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
          break;
        }
      }
    }
    return cookieValue;
  }
</script>
{% endblock %}
