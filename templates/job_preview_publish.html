{% extends 'main.html' %} {% load static %} {% load i18n %} {% block content %}
<link
  rel="stylesheet"
  href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
/>
<div class="container">
  <h1>{% trans "Publish Job" %}</h1>

  <!-- Final Review Section -->
  <div class="review-container">
    <h2>{% trans "Final Review" %}</h2>
    <p class="review-hint">{% trans "Please review the job details before publishing." %}</p>

    <!-- Job Summary -->
    <div class="review-section">
      <div class="review-header">
        <h3>{% trans "Job Summary" %}</h3>
      </div>
      <div class="summary-grid" id="job-summary">
        <!-- Job summary data will be loaded here -->
        <div class="summary-loading">{% trans "Loading job details..." %}</div>
      </div>
    </div>

    <!-- Job Description -->
    <div class="review-section">
      <div class="review-header">
        <h3>{% trans "Job Description" %}</h3>
      </div>
      <div class="description-preview" id="job-description">
        <!-- Job description will be loaded here -->
        <div class="description-loading">{% trans "Loading job description..." %}</div>
      </div>
    </div>
  </div>

  <!-- Portal Selection Section -->
  <div class="portals-container">
    <h2>{% trans "Publish To" %}</h2>
    <p class="portals-hint">
      {% trans "Select the job portals where you want to publish this job posting. <br> <br> <i> if the portal you want to publish to is grayed out, it means that you have not yet adjusted the related configuration settings. </i>" %}
    </p>

    <div class="portals-grid">
      <!-- LinkedIn -->
      <div
        class="portal-card {% if not active_portals.LinkedIn %} disabled {% endif %}"
      >
        <div class="portal-checkbox">
          <input type="checkbox" id="linkedin" class="portal-checkbox-input" />
          <label for="linkedin" class="portal-checkbox-label"></label>
        </div>
        <div class="portal-content">
          <img
            src="https://upload.wikimedia.org/wikipedia/commons/8/81/LinkedIn_icon.svg"
            alt="LinkedIn"
            class="portal-logo"
          />
          <h3 class="portal-name">LinkedIn</h3>
          <p class="portal-description">
            {% trans "Professional networking platform with over 750 million users worldwide. Selecting this option will open a new tab for you to complete the job posting." %}
          </p>
        </div>
      </div>

      <!-- Glassdoor -->
      <div
        class="portal-card {% if not active_portals.Glassdoor %} disabled {% endif %}"
      >
        <div class="portal-checkbox">
          <input type="checkbox" id="glassdoor" class="portal-checkbox-input" />
          <label for="glassdoor" class="portal-checkbox-label"></label>
        </div>
        <div class="portal-content">
          <img
            src="https://www.svgrepo.com/show/331409/glassdoor.svg"
            alt="Glassdoor"
            class="portal-logo"
          />
          <h3 class="portal-name">Glassdoor</h3>
          <p class="portal-description">
            {% trans "Job and company review site focusing on workplace transparency. Selecting this option will open a new tab for you to complete the job posting." %}
          </p>
        </div>
      </div>

        <!-- Workloupe, should always be selected as it's the internal platform -->
        <div
          class="portal-card workloupe{% if not active_portals.Workloupe %} disabled {% endif %}"
        >
          <div class="portal-checkbox">
            <input type="checkbox" id="workloupe" class="portal-checkbox-input" checked disabled/>
            <label for="workloupe" class="portal-checkbox-label"></label>
          </div>
          <div class="portal-content">
            <img
          src="{% static 'workloupe_logo.svg' %}"
          alt="workloupe"
          class="portal-logo"
            />
            <h3 class="portal-name">Workloupe</h3>
            <p class="portal-description">
          {% trans "Specialized job platform for tech and creative professionals powered by Workloupe. Workloupe is 100% free to use." %}
            </p>
          </div>
        </div>


        <!-- Himalayas -->
        <div
          class="portal-card {% if not active_portals.Himalayas %} disabled {% endif %}"
        >
          <div class="portal-checkbox">
            <input type="checkbox" id="himalayas" class="portal-checkbox-input" />
            <label for="himalayas" class="portal-checkbox-label"></label>
          </div>
          <div class="portal-content">
            <img
          src="{% static 'himalayas_logo.jpeg' %}"
          alt="Himalayas"
          class="portal-logo"
            />
            <h3 class="portal-name">Himalayas</h3>
            <p class="portal-description">
          {% trans "One of the biggest remote job focused job platforms in the world. Posting to Himalayas is free. " %}
            </p>
          </div>
        </div>

        <!-- PostJobFree -->
        <div
          class="portal-card {% if not active_portals.PostJobFree %} disabled {% endif %}"
        >
          <div class="portal-checkbox">
            <input type="checkbox" id="postjobfree" class="portal-checkbox-input" />
            <label for="postjobfree" class="portal-checkbox-label"></label>
          </div>
          <div class="portal-content">
            <img
          src="{% static 'postjobfree_logo.jpg' %}"
          alt="PostJobFree"
          class="portal-logo"
          style="width: 80px;"
            />
            <h3 class="portal-name">PostJobFree</h3>
            <p class="portal-description">
            {% trans "PostJobFree has more than 7 million jobs, and it's free to post to. Their job portal is focused on simplicity and ease of use." %}
            </p>
          </div>
        </div>
    </div>
  </div>

  <!-- Actions Section -->
  <div class="actions-container">
    <div class="left-buttons">
      <!-- Back button -->
      <button class="back-btn" id="back-btn">{% trans "Back" %}</button>
      <a href="{% url 'feed' %}" class="btn back-btn discard">{% trans "Discard" %}</a>
    </div>
    <div class="right-buttons">
      <button class="publish-btn" id="publish-btn">{% trans "Publish Job" %}</button>
    </div>
  </div>
</div>

<style>
  :root {
    --primary: #4a6cf7;
    --primary-hover: #3859e9;
    --success: #28a745;
    --secondary: #f5f8ff;
    --text-color: #333;
    --border-color: #ddd;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  }

  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  }

  body {
    background-color: #f9fafc;
    color: var(--text-color);
    line-height: 1.6;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
  }

  h1,
  h2,
  h3 {
    color: #252b42;
  }

  h1 {
    font-size: 28px;
    margin-bottom: 20px;
  }

  h2 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 10px;
  }

  h3 {
    font-size: 16px;
    font-weight: 600;
  }

  /* Review Container */
  .review-container {
    background-color: white;
    border-radius: 8px;
    padding: 30px;
    box-shadow: var(--shadow);
    margin-bottom: 30px;
  }

  .review-hint,
  .portals-hint {
    color: #666;
    margin-bottom: 20px;
    font-size: 15px;
  }

  .review-section {
    margin-bottom: 30px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    overflow: hidden;
  }

  .review-section:last-child {
    margin-bottom: 0;
  }

  .review-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: var(--secondary);
    border-bottom: 1px solid var(--border-color);
  }

  /* Summary Grid */
  .summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    padding: 20px;
  }

  .summary-item {
    padding: 15px;
    background-color: var(--secondary);
    border-radius: 6px;
  }

  .summary-label {
    font-weight: 600;
    font-size: 14px;
    color: #555;
    margin-bottom: 5px;
  }

  .summary-value {
    font-size: 16px;
  }

  .summary-skills {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .skill-tag {
    background-color: var(--primary);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 13px;
  }

  .benefit-tag {
    background-color: rgb(44, 44, 44);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 13px;
  }

  .summary-loading,
  .description-loading {
    text-align: center;
    padding: 20px;
    color: #888;
    font-style: italic;
  }

  /* Description Preview */
  .description-preview {
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
  }

  /* Portals Container */
  .portals-container {
    background-color: white;
    border-radius: 8px;
    padding: 30px;
    box-shadow: var(--shadow);
    margin-bottom: 30px;
  }

  .portals-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
  }

  .portal-card {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    display: flex;
    transition: all 0.3s;
    cursor: pointer;
  }

  .portal-card.disabled {
    pointer-events: none;
    background-color: #f0f0f0;
    border-color: #ccc;
    color: #999;
  }

  .portal-card.workloupe {
    pointer-events: none;
    border-color: var(--primary);
  }

  .portal-card:hover {
    border-color: var(--primary);
    box-shadow: 0 4px 12px rgba(74, 108, 247, 0.1);
  }

  .portal-checkbox {
    margin-right: 15px;
    display: flex;
    align-items: flex-start;
  }

  .portal-checkbox-input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
  }

  .portal-checkbox-label {
    position: relative;
    display: inline-block;
    width: 22px;
    height: 22px;
    background-color: white;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    cursor: pointer;
  }

  .portal-checkbox-input:checked ~ .portal-checkbox-label {
    background-color: var(--primary);
    border-color: var(--primary);
  }

  .portal-checkbox-input:checked ~ .portal-checkbox-label:after {
    content: "";
    position: absolute;
    left: 7px;
    top: 3px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
  }

  .portal-content {
    flex: 1;
  }

  .portal-logo {
    width: 40px;
    height: 40px;
    object-fit: contain;
    margin-bottom: 10px;
  }

  .portal-name {
    margin-bottom: 5px;
  }

  .portal-description {
    font-size: 14px;
    color: #666;
  }

  /* Actions Container */
  .actions-container {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
  }

  .right-buttons {
    display: flex;
    gap: 10px;
  }

  .back-btn,
  .publish-btn {
    padding: 12px 30px;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
  }

  .back-btn {
    background-color: white;
    color: var(--text-color);
    border: 1px solid var(--border-color);
  }

  .discard {
    border: 1px solid rgb(100, 24, 34);
  }

  .back-btn:hover {
    background-color: #f5f5f5;
  }

  .publish-btn {
    background-color: var(--success);
    color: white;
    border: none;
  }

  .publish-btn:hover {
    background-color: #218838;
  }

  .success-message {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .success-icon {
    font-size: 48px;
    color: var(--success);
    margin-right: 20px;
  }

  .portal-summary {
    background-color: var(--secondary);
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
  }

  .portal-summary ul {
    list-style-type: none;
    margin-left: 10px;
  }

  .portal-summary li {
    padding: 5px 0;
    display: flex;
    align-items: center;
  }

  .portal-summary li:before {
    content: "•";
    color: var(--primary);
    font-weight: bold;
    display: inline-block;
    width: 1em;
    margin-left: -1em;
  }

  .view-job-btn,
  .create-new-btn {
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
  }

  .view-job-btn {
    background-color: var(--primary);
    color: white;
    border: none;
  }

  .view-job-btn:hover {
    background-color: var(--primary-hover);
  }

  .create-new-btn {
    background-color: white;
    color: var(--text-color);
    border: 1px solid var(--border-color);
  }

  .create-new-btn:hover {
    background-color: #f5f5f5;
  }

  /* Media Queries */
  @media (max-width: 768px) {
    .portals-grid {
      grid-template-columns: 1fr;
    }

    .actions-container {
      flex-direction: column;
      gap: 15px;
    }

    .right-buttons {
      width: 100%;
      justify-content: space-between;
    }

    .back-btn,
    .publish-btn {
      width: 100%;
      padding: 12px 15px;
    }
  }
</style>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    // Load job data from sessionStorage
    loadJobData();

    // Setup portal selection
    setupPortalSelection();

    // Setup button actions
    setupButtonActions();
  });

  // Load job data from sessionStorage
  function loadJobData() {
    // Load job summary
    const summaryContainer = document.getElementById("job-summary");
    const jobData = JSON.parse(sessionStorage.getItem("jobFormData"));

    if (!jobData) {
      summaryContainer.innerHTML = `
                <div class="summary-item" style="grid-column: 1 / -1;">
                    <p>{% trans "No job information found. Please go back and fill out the job details form. " %}</p>
                </div>
            `;
    } else {
      // Clear loading message
      summaryContainer.innerHTML = "";

      // Create summary items for each field
      const summaryHTML = `
                <div class="summary-item">
                    <div class="summary-label">{% trans "Role Title" %}</div>
                    <div class="summary-value">${jobData.roleTitle}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">{% trans "Office Location" %}</div>
                    <div class="summary-value">${jobData.officeLocation}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">{% trans "Work Schedule" %}</div>
                    <div class="summary-value">${formatSchedule(
                      jobData.workSchedule
                    )}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">{% trans "Office Schedule" %}</div>
                    <div class="summary-value">${formatSchedule(
                      jobData.officeSchedule
                    )}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">{% trans "Department" %}</div>
                    <div class="summary-value">${jobData.department || 'Not specified'}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">{% trans "Salary Details" %}</div>
                    <div class="summary-value">From ${jobData.salaryMin} to ${
        jobData.salaryMax
      } ${jobData.salaryCurrency}</div>
                </div>

                <div class="summary-item" style="grid-column: 1 / -1;">
                    <div class="summary-label">{% trans "Benefits & Highlights" %}</div>
                    <div class="summary-skills">
                    ${
                      jobData.benefits && jobData.benefits.length > 0
                        ? jobData.benefits
                            .map(
                              (benefit) =>
                                `<div class="benefit-tag">${benefit}</div>`
                            )
                            .join("")
                        : "<span>No benefits specified</span>"
                    }
                    </div>
                </div>
                
                <div class="summary-item" style="grid-column: 1 / -1;">
                    <div class="summary-label">{% trans "Skills" %}</div>
                    <div class="summary-skills">
                        ${
                          jobData.skills && jobData.skills.length > 0
                            ? jobData.skills
                                .map(
                                  (skill) =>
                                    `<div class="skill-tag">${skill}</div>`
                                )
                                .join("")
                            : "<span>No skills specified</span>"
                        }
                    </div>
                </div>
            `;

      summaryContainer.innerHTML = summaryHTML;
    }

    // Load job description
    const descriptionContainer = document.getElementById("job-description");
    const jobDescription = sessionStorage.getItem("jobDescription");

    if (!jobDescription) {
      descriptionContainer.innerHTML = `
                <p>{% trans "No job description found. Please go back and create a job description." %}</p>
            `;
    } else {
      descriptionContainer.innerHTML = jobDescription;
    }
  }

  // Format schedule values for display
  function formatSchedule(value) {
    if (!value) return "Not specified";

    // Convert kebab-case to Title Case with spaces
    return value
      .split("-")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  }

  // Setup portal selection functionality
  function setupPortalSelection() {
    // Make portal cards clickable to toggle checkboxes
    const portalCards = document.querySelectorAll(".portal-card");

    portalCards.forEach((card) => {
      card.addEventListener("click", function (e) {
        // Don't toggle if clicking directly on the checkbox (it will toggle itself)
        if (e.target.type !== "checkbox") {
          const checkbox = this.querySelector('input[type="checkbox"]');
          checkbox.checked = !checkbox.checked;
          // print all the selected checkboxes
          console.log("Selected checkboxes:", document.querySelectorAll('.portal-checkbox-input:checked'));
        }
      });
    });
  }

  // Setup button actions
  function setupButtonActions() {
    // Back button
    document.getElementById("back-btn").addEventListener("click", function () {
      window.location.href = "{% url 'description' %}".trim();
    });

    // Publish button
    document
      .getElementById("publish-btn")
      .addEventListener("click", function () {
        // log
        console.log("Publish button clicked");

        const jobData = JSON.parse(sessionStorage.getItem("jobFormData"));
        const jobDescription = sessionStorage.getItem("jobDescription");

        // Get selected portals
        const selectedPortals = [];
        const checkboxes = document.querySelectorAll(
          ".portal-checkbox-input:checked"
        );

        checkboxes.forEach((checkbox) => {
          const portalName =
            checkbox.parentElement.nextElementSibling.querySelector(
              ".portal-name"
            ).textContent;
          selectedPortals.push(portalName);
        });

        // Check if at least one portal is selected
        if (selectedPortals.length === 0) {
          alert("Please select at least one job portal to publish to.");
          return;
        }

        if (!jobData) {
          alert("No job data found to publish.");
          return;
        }

        console.log("Selected portals:", selectedPortals);

        // Add the job description to the jobData object
        jobData.description = jobDescription;
        jobData.portals = selectedPortals;

        // Send the data to the backend
        fetch("/save-published-job/", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-CSRFToken": getCookie("csrftoken"), // Ensure CSRF token is included
          },
          body: JSON.stringify(jobData),
        })
          .then((response) => response.json())
          .then((data) => {
            if (data.success) {
              window.location.href =
                "{% url 'jobs' %}?department=&status=&location=&date=today";
              // Optionally, redirect and show a success message on jobs page.
            } else {
              alert(
                "Failed to publish job: " + (data.error || "Unknown error")
              );
            }
          })
          .catch((error) => {
            console.error("Error:", error);
            alert("An error occurred while publishing the job.");
          });
      });

    // Helper function to get CSRF token
    function getCookie(name) {
      let cookieValue = null;
      if (document.cookie && document.cookie !== "") {
        const cookies = document.cookie.split(";");
        for (let i = 0; i < cookies.length; i++) {
          const cookie = cookies[i].trim();
          if (cookie.substring(0, name.length + 1) === name + "=") {
            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
            break;
          }
        }
      }
      return cookieValue;
    }
  }
</script>
{% endblock %}
