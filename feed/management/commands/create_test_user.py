from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from feed.models import Employee, Employer


class Command(BaseCommand):
    help = 'Create a test user with employee record for testing authentication'

    def add_arguments(self, parser):
        parser.add_argument('--email', type=str, help='User email', default='<EMAIL>')
        parser.add_argument('--password', type=str, help='User password', default='testpass123')
        parser.add_argument('--employer-name', type=str, help='Employer name', default='Test Company')

    def handle(self, *args, **options):
        email = options['email']
        password = options['password']
        employer_name = options['employer_name']
        
        self.stdout.write(self.style.SUCCESS('=== Creating Test User ==='))
        
        # Create or get employer
        employer, created = Employer.objects.get_or_create(
            employer_name=employer_name,
            defaults={
                'employer_email': f'admin@{employer_name.lower().replace(" ", "")}.com',
                'employer_website': f'https://{employer_name.lower().replace(" ", "")}.com',
                'employer_description': f'{employer_name} - Test company for authentication testing',
            }
        )
        
        if created:
            self.stdout.write(f'✅ Created employer: {employer.employer_name} (ID: {employer.employer_id})')
        else:
            self.stdout.write(f'📋 Using existing employer: {employer.employer_name} (ID: {employer.employer_id})')
        
        # Create or get user
        username = email.split('@')[0]  # Use email prefix as username
        user, created = User.objects.get_or_create(
            email=email,
            defaults={
                'username': username,
                'first_name': 'Test',
                'last_name': 'User',
                'is_active': True,
            }
        )
        
        if created:
            user.set_password(password)
            user.save()
            self.stdout.write(f'✅ Created user: {user.username} ({user.email})')
        else:
            # Update password if user exists
            user.set_password(password)
            user.save()
            self.stdout.write(f'📋 Updated existing user: {user.username} ({user.email})')
        
        # Create or get employee
        employee, created = Employee.objects.get_or_create(
            user=user,
            defaults={
                'employer_id': employer,
                'role': 'Administrator',
                'status': 'Active',
            }
        )
        
        if created:
            self.stdout.write(f'✅ Created employee: {employee.role} - Status: {employee.status}')
        else:
            # Update employee if exists
            employee.employer_id = employer
            employee.role = 'Administrator'
            employee.status = 'Active'
            employee.save()
            self.stdout.write(f'📋 Updated existing employee: {employee.role} - Status: {employee.status}')
        
        self.stdout.write(self.style.SUCCESS('\n=== Test User Created Successfully! ==='))
        self.stdout.write(f'🔑 Login credentials:')
        self.stdout.write(f'   Email: {email}')
        self.stdout.write(f'   Password: {password}')
        self.stdout.write(f'   Username: {username} (alternative login)')
        self.stdout.write(f'🏢 Company: {employer.employer_name}')
        self.stdout.write(f'👤 Role: {employee.role}')
        self.stdout.write(f'\n💡 You can now login at https://canvider-ats-production-r7kmx.ondigitalocean.app/signin/ with either:')
        self.stdout.write(f'   - Email: {email}')
        self.stdout.write(f'   - Username: {username}')
        self.stdout.write(f'   - Password: {password}')
