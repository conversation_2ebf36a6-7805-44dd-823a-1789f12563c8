from django.core.management.base import BaseCommand
from django.db import connection, transaction
import uuid


class Command(BaseCommand):
    help = 'Fix Employee employer_id references after UUID migration'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be changed without making actual changes',
        )
        parser.add_argument(
            '--auto-assign',
            action='store_true',
            help='Automatically assign all employees to the first available employer',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        auto_assign = options['auto_assign']
        
        self.stdout.write(self.style.SUCCESS('=== Fixing Employee Employer References ==='))
        
        with connection.cursor() as cursor:
            # First, check the current state
            self.stdout.write('\n1. Checking current state...')
            
            # Get all employers
            cursor.execute("SELECT employer_id, employer_name FROM feed_employer ORDER BY employer_name;")
            employers = cursor.fetchall()
            
            self.stdout.write(f'Found {len(employers)} employers:')
            for i, (emp_id, emp_name) in enumerate(employers, 1):
                self.stdout.write(f'  {i}. {emp_name} (ID: {emp_id})')
            
            # Get all employees with their current employer_id values
            cursor.execute("""
                SELECT e.user_id, u.username, e.employer_id 
                FROM feed_employee e 
                JOIN auth_user u ON e.user_id = u.id 
                ORDER BY u.username;
            """)
            employees = cursor.fetchall()
            
            self.stdout.write(f'\nFound {len(employees)} employees:')
            for user_id, username, emp_id in employees:
                self.stdout.write(f'  User: {username} (ID: {user_id}), Current Employer ID: {emp_id} (type: {type(emp_id)})')
            
            if not employers:
                self.stdout.write(self.style.ERROR('No employers found! Cannot proceed.'))
                return
            
            if not employees:
                self.stdout.write(self.style.WARNING('No employees found.'))
                return
            
            # Determine the mapping strategy
            if auto_assign:
                # Assign all employees to the first employer
                target_employer_id = employers[0][0]
                target_employer_name = employers[0][1]
                
                self.stdout.write(f'\n2. Auto-assigning all employees to: {target_employer_name}')
                
                if dry_run:
                    self.stdout.write(self.style.WARNING('DRY RUN: Would update all employees to employer_id = %s'), target_employer_id)
                else:
                    with transaction.atomic():
                        cursor.execute(
                            "UPDATE feed_employee SET employer_id = %s;",
                            [target_employer_id]
                        )
                        updated_count = cursor.rowcount
                        self.stdout.write(self.style.SUCCESS(f'Updated {updated_count} employee records'))
            
            else:
                # Interactive assignment
                self.stdout.write('\n2. Interactive assignment mode')
                self.stdout.write('Please choose an employer for each employee:')
                
                updates = []
                
                for user_id, username, current_emp_id in employees:
                    self.stdout.write(f'\nEmployee: {username}')
                    self.stdout.write(f'Current employer_id: {current_emp_id}')
                    self.stdout.write('Available employers:')
                    
                    for i, (emp_id, emp_name) in enumerate(employers, 1):
                        self.stdout.write(f'  {i}. {emp_name}')
                    
                    while True:
                        try:
                            choice = input(f'Choose employer for {username} (1-{len(employers)}, or "s" to skip): ').strip()
                            
                            if choice.lower() == 's':
                                self.stdout.write(f'Skipping {username}')
                                break
                            
                            choice_num = int(choice)
                            if 1 <= choice_num <= len(employers):
                                selected_employer = employers[choice_num - 1]
                                updates.append((user_id, username, selected_employer[0], selected_employer[1]))
                                self.stdout.write(f'Will assign {username} to {selected_employer[1]}')
                                break
                            else:
                                self.stdout.write('Invalid choice. Please try again.')
                        
                        except (ValueError, KeyboardInterrupt):
                            self.stdout.write('Invalid input or cancelled. Skipping this employee.')
                            break
                
                # Apply updates
                if updates:
                    self.stdout.write(f'\n3. Applying {len(updates)} updates...')
                    
                    if dry_run:
                        self.stdout.write(self.style.WARNING('DRY RUN: Would make the following updates:'))
                        for user_id, username, new_emp_id, emp_name in updates:
                            self.stdout.write(f'  {username} -> {emp_name} ({new_emp_id})')
                    else:
                        with transaction.atomic():
                            for user_id, username, new_emp_id, emp_name in updates:
                                cursor.execute(
                                    "UPDATE feed_employee SET employer_id = %s WHERE user_id = %s;",
                                    [new_emp_id, user_id]
                                )
                                self.stdout.write(f'✅ Updated {username} -> {emp_name}')
                            
                            self.stdout.write(self.style.SUCCESS(f'Successfully updated {len(updates)} employee records'))
                else:
                    self.stdout.write('No updates to apply.')
            
            # Verify the results
            self.stdout.write('\n4. Verification...')
            cursor.execute("""
                SELECT u.username, e.employer_id, emp.employer_name
                FROM feed_employee e 
                JOIN auth_user u ON e.user_id = u.id 
                JOIN feed_employer emp ON e.employer_id = emp.employer_id
                ORDER BY u.username;
            """)
            
            verified_employees = cursor.fetchall()
            
            if verified_employees:
                self.stdout.write(self.style.SUCCESS('✅ All employee-employer relationships verified:'))
                for username, emp_id, emp_name in verified_employees:
                    self.stdout.write(f'  {username} -> {emp_name} ({emp_id})')
            else:
                self.stdout.write(self.style.ERROR('❌ No valid employee-employer relationships found'))
            
            self.stdout.write(self.style.SUCCESS('\n=== Fix Complete ==='))
