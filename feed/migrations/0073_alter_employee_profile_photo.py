# Generated by Django 4.2.23 on 2025-07-22 02:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('feed', '0072_alter_employee_permissions_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='employee',
            name='profile_photo',
            field=models.TextField(blank=True, default='data:image/png;base64,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', null=True),
        ),
    ]
