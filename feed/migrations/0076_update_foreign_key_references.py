# Generated by Django 4.2.23 on 2025-07-24 12:45

from django.db import migrations, models


def update_foreign_key_references(apps, schema_editor):
    """
    Step 2: Update all foreign key references to use the new UUIDs
    """
    from django.db import connection
    
    with connection.cursor() as cursor:
        # Add temporary UUID columns for foreign keys and update them
        foreign_key_updates = [
            # Table, FK column, referenced table, referenced PK column
            ('feed_application', 'candidate_id', 'feed_candidate', 'candidate_id'),
            ('feed_application', 'vacancy_id', 'feed_vacancy', 'vacancy_id'),  # Will need to handle vacancy separately
            ('feed_applicationcomment', 'application_id', 'feed_application', 'application_id'),
            ('feed_applicationcomment', 'commented_by_id', 'auth_user', 'id'),  # Auth user remains integer
            ('feed_applicationcvtext', 'application_id', 'feed_application', 'application_id'),
            ('feed_applicationstate', 'application_id', 'feed_application', 'application_id'),
            ('feed_applicationstate', 'committed_by_id', 'auth_user', 'id'),  # Auth user remains integer
            ('feed_talentpool', 'employer_id', 'feed_employer', 'employer_id'),
            ('feed_talentpool', 'added_by_id', 'auth_user', 'id'),  # Auth user remains integer
            ('feed_talentrequest', 'committed_by_id', 'auth_user', 'id'),  # Auth user remains integer
            ('feed_talentrequest', 'employer_id', 'feed_employer', 'employer_id'),
            ('feed_talentrequest', 'vacancy_id', 'feed_vacancy', 'vacancy_id'),  # Will need to handle vacancy separately
            ('feed_portalconfigurations', 'employer_id', 'feed_employer', 'employer_id'),
        ]
        
        for table, fk_column, ref_table, ref_pk_column in foreign_key_updates:
            # Skip auth_user and vacancy references for now as they remain integers
            if ref_table in ['auth_user', 'feed_vacancy']:
                continue
                
            temp_fk_column = f"{fk_column}_uuid_temp"
            temp_ref_pk = f"{ref_pk_column}_uuid_temp"
            
            try:
                # Add temporary UUID foreign key column
                cursor.execute(f"ALTER TABLE {table} ADD COLUMN {temp_fk_column} UUID;")
                
                # Update the temporary FK column with corresponding UUID from referenced table
                update_query = f"""
                UPDATE {table} 
                SET {temp_fk_column} = {ref_table}.{temp_ref_pk}
                FROM {ref_table} 
                WHERE {table}.{fk_column} = {ref_table}.{ref_pk_column}
                """
                cursor.execute(update_query)
                
            except Exception as e:
                print(f"Error updating FK {table}.{fk_column}: {e}")


def reverse_foreign_key_updates(apps, schema_editor):
    """
    Reverse the foreign key updates by dropping temporary columns
    """
    from django.db import connection
    
    with connection.cursor() as cursor:
        foreign_key_columns = [
            ('feed_application', 'candidate_id'),
            ('feed_applicationcomment', 'application_id'),
            ('feed_applicationcvtext', 'application_id'),
            ('feed_applicationstate', 'application_id'),
            ('feed_talentpool', 'employer_id'),
            ('feed_talentrequest', 'employer_id'),
            ('feed_portalconfigurations', 'employer_id'),
        ]
        
        for table, fk_column in foreign_key_columns:
            temp_fk_column = f"{fk_column}_uuid_temp"
            try:
                cursor.execute(f"ALTER TABLE {table} DROP COLUMN IF EXISTS {temp_fk_column};")
            except Exception as e:
                print(f"Error dropping FK column {table}.{temp_fk_column}: {e}")


class Migration(migrations.Migration):

    dependencies = [
        ('feed', '0075_alter_application_application_id_and_more'),
    ]

    operations = [
        # Step 2: Update foreign key references
        migrations.RunPython(
            update_foreign_key_references,
            reverse_foreign_key_updates,
        ),
    ]
