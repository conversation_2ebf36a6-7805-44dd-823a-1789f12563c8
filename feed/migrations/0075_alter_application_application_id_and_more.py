# Generated by Django 4.2.23 on 2025-07-24 11:34

from django.db import migrations, models
import uuid


def generate_uuid_for_existing_records(apps, schema_editor):
    """
    Step 1: Add temporary UUID columns and populate them with new UUIDs
    """
    # We'll handle this in raw SQL to ensure proper UUID generation
    from django.db import connection
    
    with connection.cursor() as cursor:
        # Create mapping tables to store old_id -> new_uuid relationships
        tables_and_columns = [
            ('feed_application', 'application_id'),
            ('feed_applicationcomment', 'comment_id'),
            ('feed_applicationcvtext', 'cv_text_id'),
            ('feed_applicationstate', 'state_id'),
            ('feed_candidate', 'candidate_id'),
            ('feed_employer', 'employer_id'),
            ('feed_portalconfigurations', 'config_id'),
            ('feed_potentialemployer', 'id'),
            ('feed_talentpool', 'talent_id'),
            ('feed_talentrequest', 'id'),
        ]
        
        # Add temporary UUID columns
        for table, pk_column in tables_and_columns:
            temp_column = f"{pk_column}_uuid_temp"
            try:
                cursor.execute(f"ALTER TABLE {table} ADD COLUMN {temp_column} UUID;")
                # Generate UUIDs for existing records
                cursor.execute(f"UPDATE {table} SET {temp_column} = gen_random_uuid();")
            except Exception as e:
                print(f"Error processing {table}: {e}")


def reverse_uuid_generation(apps, schema_editor):
    """
    Reverse the UUID generation by dropping the temporary columns
    """
    from django.db import connection
    
    with connection.cursor() as cursor:
        tables_and_columns = [
            ('feed_application', 'application_id'),
            ('feed_applicationcomment', 'comment_id'),
            ('feed_applicationcvtext', 'cv_text_id'),
            ('feed_applicationstate', 'state_id'),
            ('feed_candidate', 'candidate_id'),
            ('feed_employer', 'employer_id'),
            ('feed_portalconfigurations', 'config_id'),
            ('feed_potentialemployer', 'id'),
            ('feed_talentpool', 'talent_id'),
            ('feed_talentrequest', 'id'),
        ]
        
        for table, pk_column in tables_and_columns:
            temp_column = f"{pk_column}_uuid_temp"
            try:
                cursor.execute(f"ALTER TABLE {table} DROP COLUMN IF EXISTS {temp_column};")
            except Exception as e:
                print(f"Error dropping column from {table}: {e}")


class Migration(migrations.Migration):

    dependencies = [
        ('feed', '0074_alter_employee_profile_photo'),
    ]

    operations = [
        # Step 1: Generate UUIDs for existing records
        migrations.RunPython(
            generate_uuid_for_existing_records,
            reverse_uuid_generation,
        ),
    ]
