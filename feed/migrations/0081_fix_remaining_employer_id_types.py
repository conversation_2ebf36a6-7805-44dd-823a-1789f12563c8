# Generated manually to fix remaining employer_id foreign key types

from django.db import migrations, models
import uuid


def fix_remaining_employer_ids(apps, schema_editor):
    """
    Fix the remaining models' employer_id foreign keys to use UUID instead of integer
    """
    from django.db import connection
    
    # Models that need fixing
    models_to_fix = [
        ('feed_officelocation', 'OfficeLocation'),
        ('feed_workschedule', 'WorkSchedule'),  
        ('feed_officeschedule', 'OfficeSchedule'),
        ('feed_department', 'Department')
    ]
    
    with connection.cursor() as cursor:
        print("=== Fixing remaining employer_id foreign keys ===")
        
        # Get the first employer (or create one if none exists)
        print("1. Getting employer for assignment...")
        cursor.execute("SELECT employer_id, employer_name FROM feed_employer ORDER BY employer_created_at LIMIT 1;")
        employer = cursor.fetchone()
        
        if not employer:
            # If no employer exists, create a default one
            print("   No employers found, creating default employer...")
            default_employer_id = str(uuid.uuid4())
            cursor.execute("""
                INSERT INTO feed_employer (
                    employer_id, employer_name, employer_email, employer_website, 
                    employer_description, employer_status, employer_created_at
                ) VALUES (
                    %s, 'Default Company', '<EMAIL>', 'https://company.com',
                    'Default company created during migration', 'Active', NOW()
                );
            """, [default_employer_id])
            employer_id = default_employer_id
            employer_name = 'Default Company'
        else:
            employer_id = employer[0]
            employer_name = employer[1]
        
        print(f"   Using employer: {employer_name} ({employer_id})")
        
        # Process each model
        for table_name, model_name in models_to_fix:
            print(f"\n2. Processing {model_name} ({table_name})...")
            
            # Check current data
            cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
            record_count = cursor.fetchone()[0]
            print(f"   Found {record_count} {model_name.lower()} records")
            
            # Add temporary UUID column
            print(f"   Adding temporary UUID column...")
            cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN employer_id_uuid UUID;")
            
            # Update all records to point to the selected employer
            if record_count > 0:
                cursor.execute(
                    f"UPDATE {table_name} SET employer_id_uuid = %s;",
                    [employer_id]
                )
                updated_count = cursor.rowcount
                print(f"   Updated {updated_count} {model_name.lower()} records")
            
            # Drop foreign key constraint if it exists
            constraint_name = f"{table_name}_employer_id_fkey"
            try:
                cursor.execute(f"ALTER TABLE {table_name} DROP CONSTRAINT IF EXISTS {constraint_name};")
            except Exception as e:
                print(f"   Note: {e}")
            
            # Drop any existing indexes on the old column
            index_patterns = [
                f"{table_name.replace('feed_', '')}_employer_",
                f"feed_{table_name.replace('feed_', '')}_employer_"
            ]
            
            for pattern in index_patterns:
                try:
                    # Get existing indexes
                    cursor.execute("""
                        SELECT indexname FROM pg_indexes 
                        WHERE tablename = %s AND indexname LIKE %s;
                    """, [table_name, f"{pattern}%"])
                    indexes = cursor.fetchall()
                    
                    for (index_name,) in indexes:
                        cursor.execute(f"DROP INDEX IF EXISTS {index_name};")
                        print(f"   Dropped index: {index_name}")
                except Exception as e:
                    print(f"   Note: {e}")
            
            # Drop the old integer column
            cursor.execute(f"ALTER TABLE {table_name} DROP COLUMN employer_id;")
            
            # Rename the UUID column
            cursor.execute(f"ALTER TABLE {table_name} RENAME COLUMN employer_id_uuid TO employer_id;")
            
            # Add NOT NULL constraint
            cursor.execute(f"ALTER TABLE {table_name} ALTER COLUMN employer_id SET NOT NULL;")
            
            # Add foreign key constraint
            cursor.execute(f"""
                ALTER TABLE {table_name} 
                ADD CONSTRAINT {constraint_name} 
                FOREIGN KEY (employer_id) REFERENCES feed_employer(employer_id);
            """)
            
            print(f"   ✅ {model_name} migration completed")
        
        print("\n3. Verifying the fixes...")
        
        # Verify all schema changes
        for table_name, model_name in models_to_fix:
            cursor.execute("""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = %s AND column_name = 'employer_id';
            """, [table_name])
            
            result = cursor.fetchone()
            if result and result[1] == 'uuid':
                print(f"   ✅ {model_name}: Column type is now UUID")
            else:
                raise Exception(f"   ❌ {model_name}: Column type is still {result[1] if result else 'not found'}")
        
        # Verify the data integrity
        for table_name, model_name in models_to_fix:
            cursor.execute(f"""
                SELECT COUNT(*) 
                FROM {table_name} t 
                JOIN feed_employer emp ON t.employer_id = emp.employer_id;
            """)
            
            valid_count = cursor.fetchone()[0]
            print(f"   ✅ {model_name}: {valid_count} records have valid employer references")
        
        print("\n=== All remaining models migration completed successfully ===")


def reverse_fix_remaining_employer_ids(apps, schema_editor):
    """
    This reverse operation is complex and potentially data-destructive.
    In practice, you might want to create a backup before running this migration.
    """
    print("WARNING: Reverse migration is not implemented as it's complex and risky.")
    print("If you need to reverse this, restore from a database backup.")


class Migration(migrations.Migration):

    dependencies = [
        ('feed', '0080_fix_jobtemplate_employer_id_type'),
    ]

    operations = [
        migrations.RunPython(
            fix_remaining_employer_ids,
            reverse_fix_remaining_employer_ids,
        ),
    ]
