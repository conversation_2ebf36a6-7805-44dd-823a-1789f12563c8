# Generated manually to fix JobTemplate employer_id foreign key type

from django.db import migrations, models
import uuid


def fix_jobtemplate_employer_id(apps, schema_editor):
    """
    Fix the JobTemplate employer_id foreign key to use UUID instead of integer
    """
    from django.db import connection
    
    with connection.cursor() as cursor:
        print("=== Fixing JobTemplate employer_id foreign key ===")
        
        # Step 1: Check current data
        print("1. Checking current job template data...")
        cursor.execute("SELECT COUNT(*) FROM feed_jobtemplate;")
        template_count = cursor.fetchone()[0]
        print(f"   Found {template_count} job template records")
        
        if template_count == 0:
            print("   No templates to migrate, proceeding with schema change...")
        else:
            print("   WARNING: Found existing templates. All will be assigned to the first employer.")
        
        # Step 2: Add a temporary UUID column
        print("2. Adding temporary UUID column...")
        cursor.execute("ALTER TABLE feed_jobtemplate ADD COLUMN employer_id_uuid UUID;")
        
        # Step 3: Get the first employer (or create one if none exists)
        print("3. Getting employer for assignment...")
        cursor.execute("SELECT employer_id, employer_name FROM feed_employer ORDER BY employer_created_at LIMIT 1;")
        employer = cursor.fetchone()
        
        if not employer:
            # If no employer exists, create a default one
            print("   No employers found, creating default employer...")
            default_employer_id = str(uuid.uuid4())
            cursor.execute("""
                INSERT INTO feed_employer (
                    employer_id, employer_name, employer_email, employer_website, 
                    employer_description, employer_status, employer_created_at
                ) VALUES (
                    %s, 'Default Company', '<EMAIL>', 'https://company.com',
                    'Default company created during migration', 'Active', NOW()
                );
            """, [default_employer_id])
            employer_id = default_employer_id
            employer_name = 'Default Company'
        else:
            employer_id = employer[0]
            employer_name = employer[1]
        
        print(f"   Assigning all templates to: {employer_name} ({employer_id})")
        
        # Step 4: Update all templates to point to the selected employer
        if template_count > 0:
            cursor.execute(
                "UPDATE feed_jobtemplate SET employer_id_uuid = %s;",
                [employer_id]
            )
            updated_count = cursor.rowcount
            print(f"   Updated {updated_count} template records")
        
        # Step 5: Drop the old integer column and rename the UUID column
        print("4. Replacing old column with UUID column...")
        
        # Drop foreign key constraint if it exists
        try:
            cursor.execute("ALTER TABLE feed_jobtemplate DROP CONSTRAINT IF EXISTS feed_jobtemplate_employer_id_fkey;")
        except Exception as e:
            print(f"   Note: {e}")
        
        # Drop any indexes on the old column
        try:
            cursor.execute("DROP INDEX IF EXISTS feed_jobtemplate_employer_created_idx;")
            cursor.execute("DROP INDEX IF EXISTS feed_jobtemplate_employer_title_idx;")
        except Exception as e:
            print(f"   Note: {e}")
        
        # Drop the old integer column
        cursor.execute("ALTER TABLE feed_jobtemplate DROP COLUMN employer_id;")
        
        # Rename the UUID column
        cursor.execute("ALTER TABLE feed_jobtemplate RENAME COLUMN employer_id_uuid TO employer_id;")
        
        # Add NOT NULL constraint
        cursor.execute("ALTER TABLE feed_jobtemplate ALTER COLUMN employer_id SET NOT NULL;")
        
        # Add foreign key constraint
        cursor.execute("""
            ALTER TABLE feed_jobtemplate 
            ADD CONSTRAINT feed_jobtemplate_employer_id_fkey 
            FOREIGN KEY (employer_id) REFERENCES feed_employer(employer_id);
        """)
        
        # Recreate the indexes
        cursor.execute("""
            CREATE INDEX feed_jobtemplate_employer_created_idx 
            ON feed_jobtemplate(employer_id, created_at);
        """)
        cursor.execute("""
            CREATE INDEX feed_jobtemplate_employer_title_idx 
            ON feed_jobtemplate(employer_id, title);
        """)
        
        print("5. Verifying the fix...")
        
        # Verify the schema change
        cursor.execute("""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = 'feed_jobtemplate' AND column_name = 'employer_id';
        """)
        
        result = cursor.fetchone()
        if result and result[1] == 'uuid':
            print(f"   ✅ Column type is now: {result[1]}")
        else:
            raise Exception(f"   ❌ Column type is still: {result[1] if result else 'not found'}")
        
        # Verify the data
        cursor.execute("""
            SELECT COUNT(*) 
            FROM feed_jobtemplate jt 
            JOIN feed_employer emp ON jt.employer_id = emp.employer_id;
        """)
        
        valid_count = cursor.fetchone()[0]
        print(f"   ✅ {valid_count} templates have valid employer references")
        
        print("=== JobTemplate migration completed successfully ===")


def reverse_fix_jobtemplate_employer_id(apps, schema_editor):
    """
    This reverse operation is complex and potentially data-destructive.
    In practice, you might want to create a backup before running this migration.
    """
    print("WARNING: Reverse migration is not implemented as it's complex and risky.")
    print("If you need to reverse this, restore from a database backup.")


class Migration(migrations.Migration):

    dependencies = [
        ('feed', '0079_fix_invitation_employer_id_type'),
    ]

    operations = [
        migrations.RunPython(
            fix_jobtemplate_employer_id,
            reverse_fix_jobtemplate_employer_id,
        ),
    ]
