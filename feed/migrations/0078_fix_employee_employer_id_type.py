# Generated manually to fix Employee employer_id foreign key type

from django.db import migrations, models
import uuid


def fix_employee_employer_id(apps, schema_editor):
    """
    Fix the Employee employer_id foreign key to use UUID instead of integer
    """
    from django.db import connection
    
    with connection.cursor() as cursor:
        print("=== Fixing Employee employer_id foreign key ===")
        
        # Step 1: Add a temporary UUID column
        print("1. Adding temporary UUID column...")
        cursor.execute("ALTER TABLE feed_employee ADD COLUMN employer_id_uuid UUID;")
        
        # Step 2: Get the mapping of old integer IDs to new UUIDs
        print("2. Getting employer ID mapping...")
        cursor.execute("SELECT employer_id, employer_name FROM feed_employer ORDER BY employer_name;")
        employers = cursor.fetchall()
        
        if not employers:
            raise Exception("No employers found! Cannot proceed with migration.")
        
        print(f"Found {len(employers)} employers:")
        for emp_id, emp_name in employers:
            print(f"  - {emp_name}: {emp_id}")
        
        # Step 3: For this migration, we'll assign all employees to the first employer
        # In a production environment, you'd want a more sophisticated mapping
        first_employer_id = employers[0][0]
        first_employer_name = employers[0][1]
        
        print(f"3. Assigning all employees to: {first_employer_name} ({first_employer_id})")
        
        # Update all employees to point to the first employer
        cursor.execute(
            "UPDATE feed_employee SET employer_id_uuid = %s;",
            [first_employer_id]
        )
        
        updated_count = cursor.rowcount
        print(f"   Updated {updated_count} employee records")
        
        # Step 4: Drop the old integer column and rename the UUID column
        print("4. Replacing old column with UUID column...")
        
        # Drop foreign key constraint if it exists
        try:
            cursor.execute("ALTER TABLE feed_employee DROP CONSTRAINT IF EXISTS feed_employee_employer_id_fkey;")
        except Exception as e:
            print(f"   Note: {e}")
        
        # Drop the old integer column
        cursor.execute("ALTER TABLE feed_employee DROP COLUMN employer_id;")
        
        # Rename the UUID column
        cursor.execute("ALTER TABLE feed_employee RENAME COLUMN employer_id_uuid TO employer_id;")
        
        # Add NOT NULL constraint
        cursor.execute("ALTER TABLE feed_employee ALTER COLUMN employer_id SET NOT NULL;")
        
        # Add foreign key constraint
        cursor.execute("""
            ALTER TABLE feed_employee 
            ADD CONSTRAINT feed_employee_employer_id_fkey 
            FOREIGN KEY (employer_id) REFERENCES feed_employer(employer_id);
        """)
        
        print("5. Verifying the fix...")
        
        # Verify the schema change
        cursor.execute("""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = 'feed_employee' AND column_name = 'employer_id';
        """)
        
        result = cursor.fetchone()
        if result and result[1] == 'uuid':
            print(f"   ✅ Column type is now: {result[1]}")
        else:
            raise Exception(f"   ❌ Column type is still: {result[1] if result else 'not found'}")
        
        # Verify the data
        cursor.execute("""
            SELECT COUNT(*) 
            FROM feed_employee e 
            JOIN feed_employer emp ON e.employer_id = emp.employer_id;
        """)
        
        valid_count = cursor.fetchone()[0]
        print(f"   ✅ {valid_count} employees have valid employer references")
        
        print("=== Migration completed successfully ===")


def reverse_fix_employee_employer_id(apps, schema_editor):
    """
    This reverse operation is complex and potentially data-destructive.
    In practice, you might want to create a backup before running this migration.
    """
    print("WARNING: Reverse migration is not implemented as it's complex and risky.")
    print("If you need to reverse this, restore from a database backup.")


class Migration(migrations.Migration):

    dependencies = [
        ('feed', '0077_finalize_uuid_conversion'),
    ]

    operations = [
        migrations.RunPython(
            fix_employee_employer_id,
            reverse_fix_employee_employer_id,
        ),
    ]
