# Generated by Django 4.2.23 on 2025-07-24 12:50

from django.db import migrations, models
import uuid


def finalize_uuid_conversion(apps, schema_editor):
    """
    Step 3: Replace old integer columns with UUID columns
    """
    from django.db import connection
    
    with connection.cursor() as cursor:
        # First, drop any dependent views that might be using these columns
        dependent_views = [
            'employer_cards',
            'highlighted_jobs', 
            'vacancies'
        ]
        
        for view_name in dependent_views:
            try:
                cursor.execute(f"DROP VIEW IF EXISTS {view_name} CASCADE;")
                print(f"Dropped view {view_name}")
            except Exception as e:
                print(f"Error dropping view {view_name}: {e}")
        
        # Primary key conversions
        pk_conversions = [
            ('feed_application', 'application_id'),
            ('feed_applicationcomment', 'comment_id'), 
            ('feed_applicationcvtext', 'cv_text_id'),
            ('feed_applicationstate', 'state_id'),
            ('feed_candidate', 'candidate_id'),
            ('feed_employer', 'employer_id'),
            ('feed_portalconfigurations', 'config_id'),
            ('feed_potentialemployer', 'id'),
            ('feed_talentpool', 'talent_id'),
            ('feed_talentrequest', 'id'),
        ]
        
        # Foreign key conversions
        fk_conversions = [
            ('feed_application', 'candidate_id'),
            ('feed_applicationcomment', 'application_id'),
            ('feed_applicationcvtext', 'application_id'),
            ('feed_applicationstate', 'application_id'),
            ('feed_talentpool', 'employer_id'),
            ('feed_talentrequest', 'employer_id'),
            ('feed_portalconfigurations', 'employer_id'),
        ]
        
        # Step 1: Drop all constraints in a simple way
        all_tables = [table for table, _ in pk_conversions]
        for table in all_tables:
            try:
                # Drop primary key constraint
                cursor.execute(f"ALTER TABLE {table} DROP CONSTRAINT IF EXISTS {table}_pkey CASCADE;")
                
                # Drop all foreign key constraints on this table
                cursor.execute(f"""
                    SELECT constraint_name 
                    FROM information_schema.table_constraints 
                    WHERE table_name = '{table}' 
                    AND constraint_type = 'FOREIGN KEY'
                """)
                
                constraints = cursor.fetchall()
                for (constraint_name,) in constraints:
                    cursor.execute(f"ALTER TABLE {table} DROP CONSTRAINT IF EXISTS {constraint_name} CASCADE;")
                    
            except Exception as e:
                print(f"Error dropping constraints for {table}: {e}")
        
        # Step 2: Replace old columns with UUID columns
        for table, column in pk_conversions:
            temp_column = f"{column}_uuid_temp"
            try:
                # Drop the old integer column with CASCADE to handle any remaining dependencies
                cursor.execute(f"ALTER TABLE {table} DROP COLUMN IF EXISTS {column} CASCADE;")
                # Rename the temporary UUID column to the original name
                cursor.execute(f"ALTER TABLE {table} RENAME COLUMN {temp_column} TO {column};")
                # Add primary key constraint
                cursor.execute(f"ALTER TABLE {table} ADD PRIMARY KEY ({column});")
                print(f"Successfully converted PK {table}.{column} to UUID")
                
            except Exception as e:
                print(f"Error converting PK {table}.{column}: {e}")
        
        # Step 3: Replace foreign key columns
        for table, column in fk_conversions:
            temp_column = f"{column}_uuid_temp"
            try:
                # Drop the old integer column with CASCADE
                cursor.execute(f"ALTER TABLE {table} DROP COLUMN IF EXISTS {column} CASCADE;")
                # Rename the temporary UUID column to the original name
                cursor.execute(f"ALTER TABLE {table} RENAME COLUMN {temp_column} TO {column};")
                print(f"Successfully converted FK {table}.{column} to UUID")
                
            except Exception as e:
                print(f"Error converting FK {table}.{column}: {e}")


def reverse_uuid_conversion(apps, schema_editor):
    """
    This reverse operation is complex and potentially data-destructive.
    In practice, you might want to create a backup before running this migration.
    """
    # This is a placeholder - in practice, you'd need to carefully reverse
    # the UUID conversion, but this is complex and risky with existing data
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('feed', '0076_update_foreign_key_references'),
    ]

    operations = [
        # Step 3: Finalize UUID conversion
        migrations.RunPython(
            finalize_uuid_conversion,
            reverse_uuid_conversion,
        ),
        
        # The field conversions are already done in the Python code above
        # No need for AlterField operations since we manually converted the columns
    ]
